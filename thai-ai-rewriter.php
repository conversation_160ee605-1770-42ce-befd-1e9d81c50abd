<?php
/**
 * Plugin Name: Thai AI Rewriter
 * Description: A WordPress plugin for rewriting Thai text using AI with user feedback learning
 * Version: 1.0.0
 * Author: Your Name
 * Text Domain: thai-ai-rewriter
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('THAI_AI_REWRITER_VERSION', '1.0.0');
define('THAI_AI_REWRITER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('THAI_AI_REWRITER_PLUGIN_PATH', plugin_dir_path(__FILE__));

class ThaiAiRewriter
{

    public function __construct()
    {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_filter('manage_thai_rewrite_work_posts_columns', array($this, 'add_rewrite_columns'));
        add_action('manage_thai_rewrite_work_posts_custom_column', array($this, 'fill_rewrite_columns'), 10, 2);
        add_action('wp_ajax_thai_ai_rewrite', array($this, 'handle_rewrite_request'));
        add_action('wp_ajax_nopriv_thai_ai_rewrite', array($this, 'handle_rewrite_request'));
        add_action('wp_ajax_thai_ai_save_feedback', array($this, 'handle_save_feedback'));
        add_action('wp_ajax_nopriv_thai_ai_save_feedback', array($this, 'handle_save_feedback'));
        add_action('wp_ajax_thai_ai_get_feedback', array($this, 'handle_get_feedback'));
        add_action('wp_ajax_nopriv_thai_ai_get_feedback', array($this, 'handle_get_feedback'));
        add_action('wp_ajax_thai_ai_clear_feedback', array($this, 'handle_clear_feedback'));
        add_action('wp_ajax_nopriv_thai_ai_clear_feedback', array($this, 'handle_clear_feedback'));
        add_action('wp_ajax_thai_ai_upload_csv', array($this, 'handle_csv_upload'));
        add_action('wp_ajax_nopriv_thai_ai_upload_csv', array($this, 'handle_csv_upload'));
        add_action('wp_ajax_thai_ai_process_csv', array($this, 'handle_csv_process'));
        add_action('wp_ajax_nopriv_thai_ai_process_csv', array($this, 'handle_csv_process'));
        add_action('wp_ajax_thai_ai_download_csv', array($this, 'handle_csv_download'));
        add_action('wp_ajax_nopriv_thai_ai_download_csv', array($this, 'handle_csv_download'));
        add_action('wp_ajax_thai_ai_save_api_key', array($this, 'handle_save_api_key'));
        add_action('wp_ajax_nopriv_thai_ai_save_api_key', array($this, 'handle_save_api_key'));
        add_action('wp_ajax_thai_ai_get_api_key', array($this, 'handle_get_api_key'));
        add_action('wp_ajax_nopriv_thai_ai_get_api_key', array($this, 'handle_get_api_key'));
        add_action('wp_ajax_thai_ai_save_word_alternatives', array($this, 'handle_save_word_alternatives'));
        add_action('wp_ajax_nopriv_thai_ai_save_word_alternatives', array($this, 'handle_save_word_alternatives'));
        add_action('wp_ajax_thai_ai_get_word_alternatives', array($this, 'handle_get_word_alternatives'));
        add_action('wp_ajax_nopriv_thai_ai_get_word_alternatives', array($this, 'handle_get_word_alternatives'));
        add_action('wp_ajax_thai_ai_save_forbidden_words', array($this, 'handle_save_forbidden_words'));
        add_action('wp_ajax_nopriv_thai_ai_save_forbidden_words', array($this, 'handle_save_forbidden_words'));
        add_action('wp_ajax_thai_ai_get_forbidden_words', array($this, 'handle_get_forbidden_words'));
        add_action('wp_ajax_nopriv_thai_ai_get_forbidden_words', array($this, 'handle_get_forbidden_words'));
        add_action('wp_ajax_thai_ai_save_instructions', array($this, 'handle_save_instructions'));
        add_action('wp_ajax_nopriv_thai_ai_save_instructions', array($this, 'handle_save_instructions'));
        add_action('wp_ajax_thai_ai_get_instructions', array($this, 'handle_get_instructions'));
        add_action('wp_ajax_nopriv_thai_ai_get_instructions', array($this, 'handle_get_instructions'));
        add_action('wp_ajax_thai_ai_get_history_stats', array($this, 'handle_get_history_stats'));
        add_action('wp_ajax_nopriv_thai_ai_get_history_stats', array($this, 'handle_get_history_stats'));
        add_action('wp_ajax_thai_ai_clear_history', array($this, 'handle_clear_history'));
        add_action('wp_ajax_nopriv_thai_ai_clear_history', array($this, 'handle_clear_history'));
        add_action('wp_ajax_thai_ai_save_combined_work', array($this, 'handle_save_combined_work'));
        add_action('wp_ajax_nopriv_thai_ai_save_combined_work', array($this, 'handle_save_combined_work'));
        add_shortcode('thai_ai_rewriter', array($this, 'render_shortcode'));
    }

    public function init()
    {
        $this->create_database_table();
        $this->register_post_types();
        $this->create_rewrite_category();
    }

    public function add_admin_menu()
    {
        add_menu_page(
            'Thai AI Rewriter',
            'Thai AI Rewriter',
            'manage_options',
            'thai-ai-rewriter',
            array($this, 'admin_page'),
            'dashicons-edit',
            30
        );

        add_submenu_page(
            'thai-ai-rewriter',
            'Feedback Database',
            'Feedback Database',
            'manage_options',
            'thai-ai-rewriter-feedback',
            array($this, 'feedback_page')
        );

        add_submenu_page(
            'thai-ai-rewriter',
            'Work History',
            'Work History',
            'manage_options',
            'edit.php?post_type=thai_rewrite_work'
        );
    }

    public function enqueue_scripts()
    {
        wp_enqueue_style('sarabun-font', 'https://fonts.googleapis.com/css2?family=Sarabun:wght@400;500;700&display=swap');
        wp_enqueue_script('thai-ai-rewriter-js', THAI_AI_REWRITER_PLUGIN_URL . 'assets/script.js', array('jquery'), '1.0.2', true);
        wp_enqueue_style('thai-ai-rewriter-css', THAI_AI_REWRITER_PLUGIN_URL . 'assets/style.css', array(), THAI_AI_REWRITER_VERSION);

        wp_localize_script('thai-ai-rewriter-js', 'thaiAiRewriter', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('thai_ai_rewriter_nonce')
        ));
    }

    public function enqueue_admin_scripts($hook)
    {
        if (strpos($hook, 'thai-ai-rewriter') === false) {
            return;
        }

        wp_enqueue_style('sarabun-font', 'https://fonts.googleapis.com/css2?family=Sarabun:wght@400;500;700&display=swap');
        wp_enqueue_script('thai-ai-rewriter-js', THAI_AI_REWRITER_PLUGIN_URL . 'assets/script.js', array('jquery'), '1.0.2', true);
        wp_enqueue_style('thai-ai-rewriter-css', THAI_AI_REWRITER_PLUGIN_URL . 'assets/style.css', array(), THAI_AI_REWRITER_VERSION);

        wp_localize_script('thai-ai-rewriter-js', 'thaiAiRewriter', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('thai_ai_rewriter_nonce')
        ));
    }

    private function create_database_table()
    {
        global $wpdb;

        $feedback_table = $wpdb->prefix . 'thai_ai_feedback';
        $csv_table = $wpdb->prefix . 'thai_ai_csv_sessions';
        $word_alternatives_table = $wpdb->prefix . 'thai_ai_word_alternatives';
        $forbidden_words_table = $wpdb->prefix . 'thai_ai_forbidden_words';
        $ai_instructions_table = $wpdb->prefix . 'thai_ai_instructions';
        $rewrite_history_table = $wpdb->prefix . 'thai_ai_rewrite_history';

        $charset_collate = $wpdb->get_charset_collate();

        $sql1 = "CREATE TABLE $feedback_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            original_text text NOT NULL,
            approved_text text NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        $sql2 = "CREATE TABLE $csv_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            session_id varchar(255) NOT NULL,
            filename varchar(255) NOT NULL,
            csv_data longtext NOT NULL,
            processed_data longtext,
            status varchar(50) DEFAULT 'uploaded',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY session_id (session_id)
        ) $charset_collate;";

        $sql3 = "CREATE TABLE $word_alternatives_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            original_word varchar(255) NOT NULL,
            alternative_words text NOT NULL,
            description text,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY original_word (original_word)
        ) $charset_collate;";

        $sql4 = "CREATE TABLE $forbidden_words_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            forbidden_word varchar(255) NOT NULL,
            reason text,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY forbidden_word (forbidden_word)
        ) $charset_collate;";

        $sql5 = "CREATE TABLE $ai_instructions_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            instruction_type varchar(50) NOT NULL,
            instruction_content longtext NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY instruction_type (instruction_type)
        ) $charset_collate;";

        $sql6 = "CREATE TABLE $rewrite_history_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            original_text longtext NOT NULL,
            rewritten_text longtext NOT NULL,
            user_ip varchar(45),
            user_agent text,
            processing_time float,
            model_used varchar(100),
            word_count_original int,
            word_count_rewritten int,
            status varchar(20) DEFAULT 'completed',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY created_at (created_at),
            KEY status (status)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql1);
        dbDelta($sql2);
        dbDelta($sql3);
        dbDelta($sql4);
        dbDelta($sql5);
        dbDelta($sql6);
    }

    private function register_post_types()
    {
        register_post_type('thai_rewrite_work', array(
            'labels' => array(
                'name' => 'Work History',
                'singular_name' => 'Rewrite Work',
                'add_new' => 'Add New Rewrite',
                'add_new_item' => 'Add New Rewrite Work',
                'edit_item' => 'Edit Rewrite Work',
                'new_item' => 'New Rewrite Work',
                'view_item' => 'View Rewrite Work',
                'search_items' => 'Search Rewrite Work',
                'not_found' => 'No rewrite work found',
                'not_found_in_trash' => 'No rewrite work found in trash',
                'menu_name' => 'Work History'
            ),
            'public' => false,
            'show_ui' => true,
            'show_in_menu' => 'edit.php',
            'show_in_admin_bar' => false,
            'show_in_nav_menus' => false,
            'can_export' => true,
            'has_archive' => false,
            'exclude_from_search' => true,
            'publicly_queryable' => false,
            'capability_type' => 'post',
            'supports' => array('title', 'editor', 'custom-fields'),
            'menu_icon' => 'dashicons-edit-page',
            'menu_position' => 25
        ));
    }

    private function create_rewrite_category()
    {
        // Check if category exists
        $category = get_term_by('slug', 'rewrite-work-history', 'category');

        if (!$category) {
            wp_insert_term(
                'Rewrite Work History',
                'category',
                array(
                    'slug' => 'rewrite-work-history',
                    'description' => 'Category for AI rewrite work history'
                )
            );
        }
    }

    public function handle_rewrite_request()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        $text = sanitize_textarea_field($_POST['text']);

        if (empty($text)) {
            wp_send_json_error('Missing text to rewrite');
        }

        // Get API key from database
        $api_key = get_option('thai_ai_rewriter_api_key', '');

        if (empty($api_key)) {
            wp_send_json_error('API key not configured. Please set your API key first.');
        }

        $examples = $this->get_feedback_examples(3);
        $rewritten_text = $this->call_typhoon_api($text, $api_key, $examples);

        if (is_wp_error($rewritten_text)) {
            wp_send_json_error($rewritten_text->get_error_message());
        } else {
            wp_send_json_success($rewritten_text);
        }
    }

    public function handle_save_feedback()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $original = sanitize_textarea_field($_POST['original']);
        $approved = sanitize_textarea_field($_POST['approved']);

        $table_name = $wpdb->prefix . 'thai_ai_feedback';

        $result = $wpdb->insert(
            $table_name,
            array(
                'original_text' => $original,
                'approved_text' => $approved
            ),
            array('%s', '%s')
        );

        if ($result === false) {
            wp_send_json_error('Failed to save feedback');
        } else {
            wp_send_json_success('Feedback saved successfully');
        }
    }

    private function get_feedback_examples($limit = 5)
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_feedback';

        // Get more examples with recent ones prioritized
        $results = $wpdb->get_results(
            $wpdb->prepare("
                SELECT original_text, approved_text, created_at 
                FROM $table_name 
                ORDER BY created_at DESC, RAND() 
                LIMIT %d
            ", $limit),
            ARRAY_A
        );

        return $results;
    }

    private function analyze_feedback_patterns()
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_feedback';

        // Get all feedback data for pattern analysis
        $all_feedback = $wpdb->get_results(
            "SELECT original_text, approved_text FROM $table_name ORDER BY created_at DESC",
            ARRAY_A
        );

        $word_patterns = array();
        $phrase_patterns = array();

        foreach ($all_feedback as $feedback) {
            $original = $feedback['original_text'];
            $approved = $feedback['approved_text'];

            // Find word-level changes
            $word_changes = $this->find_word_level_changes($original, $approved);
            foreach ($word_changes as $from => $to) {
                if (!isset($word_patterns[$from])) {
                    $word_patterns[$from] = array();
                }
                if (!isset($word_patterns[$from][$to])) {
                    $word_patterns[$from][$to] = 0;
                }
                $word_patterns[$from][$to]++;
            }

            // Find phrase-level changes
            $phrase_changes = $this->find_phrase_level_changes($original, $approved);
            foreach ($phrase_changes as $from => $to) {
                if (!isset($phrase_patterns[$from])) {
                    $phrase_patterns[$from] = array();
                }
                if (!isset($phrase_patterns[$from][$to])) {
                    $phrase_patterns[$from][$to] = 0;
                }
                $phrase_patterns[$from][$to]++;
            }
        }

        return array(
            'words' => $word_patterns,
            'phrases' => $phrase_patterns
        );
    }

    private function find_word_level_changes($original, $approved)
    {
        $changes = array();

        // Split into words
        $original_words = preg_split('/\s+/u', $original);
        $approved_words = preg_split('/\s+/u', $approved);

        // Simple word comparison (can be enhanced with better Thai tokenization)
        $min_length = min(count($original_words), count($approved_words));

        for ($i = 0; $i < $min_length; $i++) {
            $orig_word = trim($original_words[$i], '.,!?;:"()[]{}');
            $appr_word = trim($approved_words[$i], '.,!?;:"()[]{}');

            if (
                $orig_word !== $appr_word &&
                mb_strlen($orig_word) > 1 &&
                mb_strlen($appr_word) > 1 &&
                preg_match('/[\p{Thai}]/u', $orig_word) &&
                preg_match('/[\p{Thai}]/u', $appr_word)
            ) {
                $changes[$orig_word] = $appr_word;
            }
        }

        return $changes;
    }

    private function find_phrase_level_changes($original, $approved)
    {
        $changes = array();

        // Find common Thai phrase patterns
        $phrase_patterns = array(
            '/([ก-๙]+)\s*คล้ายคลึง/u',
            '/([ก-๙]+)\s*<u',
            '/([ก-๙]+)\s*มาก/u',
            '/([ก-๙]+)\s*เหมาะสม/u',
            '/([ก-๙]+)\s*น่าสนใจ/u',
            '/([ก-๙]+)\s*<u',
            '/([ก-๙]+)\s*ยอดเยี่ยม/u'
        );

        foreach ($phrase_patterns as $pattern) {
            preg_match_all($pattern, $original, $orig_matches, PREG_SET_ORDER);
            preg_match_all($pattern, $approved, $appr_matches, PREG_SET_ORDER);

            if (!empty($orig_matches) && !empty($appr_matches)) {
                for ($i = 0; $i < min(count($orig_matches), count($appr_matches)); $i++) {
                    if ($orig_matches[$i][0] !== $appr_matches[$i][0]) {
                        $changes[$orig_matches[$i][0]] = $appr_matches[$i][0];
                    }
                }
            }
        }

        // Also find 2-3 word phrases that changed
        $original_phrases = $this->extract_phrases($original, 2, 3);
        $approved_phrases = $this->extract_phrases($approved, 2, 3);

        foreach ($original_phrases as $i => $orig_phrase) {
            if (isset($approved_phrases[$i]) && $orig_phrase !== $approved_phrases[$i]) {
                $changes[$orig_phrase] = $approved_phrases[$i];
            }
        }

        return $changes;
    }

    private function extract_phrases($text, $min_words = 2, $max_words = 3)
    {
        $words = preg_split('/\s+/u', $text);
        $phrases = array();

        for ($len = $min_words; $len <= $max_words; $len++) {
            for ($i = 0; $i <= count($words) - $len; $i++) {
                $phrase = implode(' ', array_slice($words, $i, $len));
                if (preg_match('/[\p{Thai}]/u', $phrase)) {
                    $phrases[] = $phrase;
                }
            }
        }

        return $phrases;
    }

    private function apply_feedback_patterns($text, $examples)
    {
        if (empty($examples)) {
            return $text;
        }

        // Extract word replacement patterns from feedback
        $replacements = array();

        foreach ($examples as $example) {
            $original_words = $this->extract_thai_words($example['original_text']);
            $approved_words = $this->extract_thai_words($example['approved_text']);

            // Find word replacements
            foreach ($original_words as $i => $orig_word) {
                if (isset($approved_words[$i]) && $orig_word !== $approved_words[$i]) {
                    // Store the replacement pattern
                    $replacements[$orig_word] = $approved_words[$i];
                }
            }
        }

        // Apply learned replacements to the new text
        $improved_text = $text;
        foreach ($replacements as $from => $to) {
            if (mb_strlen($from) > 2) { // Only replace meaningful words
                $improved_text = str_replace($from, $to, $improved_text);
            }
        }

        return $improved_text;
    }

    private function extract_thai_words($text)
    {
        // Simple Thai word extraction (can be enhanced with proper Thai tokenizer)
        $words = preg_split('/[\s\p{P}]+/u', $text, -1, PREG_SPLIT_NO_EMPTY);
        return array_filter($words, function ($word) {
            return mb_strlen($word) > 1 && preg_match('/[\p{Thai}]/u', $word);
        });
    }

    private function call_typhoon_api($text, $api_key, $examples = array())
    {
        // Use correct model names from OpenTyphoon documentation
        $possible_models = array(
            'typhoon-v2-70b-instruct',
            'typhoon-v2-8b-instruct'
        );

        $messages = array();

        // Build and store word alternatives and forbidden words instructions
        $alternatives_guide = $this->build_stored_word_alternatives_instruction();
        $forbidden_guide = $this->build_stored_forbidden_words_instruction();

        // Get all stored instructions
        $stored_instructions = $this->get_stored_instructions();

        // Analyze feedback patterns for word/phrase level recommendations
        $patterns = $this->analyze_feedback_patterns();
        $word_recommendations = $this->build_word_recommendations($patterns['words']);
        $phrase_recommendations = $this->build_phrase_recommendations($patterns['phrases']);

        // Enhanced system message with specific word/phrase recommendations
        $feedback_instructions = '';
        if (!empty($examples)) {
            $feedback_instructions = "\n\n6. LEARN FROM FEEDBACK: Study the provided examples carefully. These show the exact writing style and word choices preferred by users. Apply similar improvements and word replacements to the new text.";
        }

        $word_guide = '';
        if (!empty($word_recommendations)) {
            $word_guide = "\n\n7. PREFERRED WORD CHOICES: Based on user feedback, prefer these word improvements:\n" . $word_recommendations;
        }

        $phrase_guide = '';
        if (!empty($phrase_recommendations)) {
            $phrase_guide = "\n\n8. PREFERRED PHRASE PATTERNS: Based on user feedback, use these phrase improvements:\n" . $phrase_recommendations;
        }

        // Use stored instructions if available, otherwise use generated ones
        $alternatives_instruction = isset($stored_instructions['word_alternatives']) ?
            "\n\n" . $stored_instructions['word_alternatives'] :
            ($alternatives_guide ? "\n\n" . $alternatives_guide : '');

        $forbidden_instruction = isset($stored_instructions['forbidden_words']) ?
            "\n\n" . $stored_instructions['forbidden_words'] :
            ($forbidden_guide ? "\n\n" . $forbidden_guide : '');

        $messages[] = array(
            'role' => 'system',
            'content' => 'You are an expert Thai copy editor. Rewrite the following Thai text to be more professional, clear, and engaging. Follow these strict rules:

1. PRESERVE ALL NUMBERS: Keep all numbers (digits, percentages, dates, quantities) exactly as they appear in the original text. Do not change, modify, or reformat any numbers.

2. NO HTML OR MARKUP: Return only plain text. Do not include any HTML tags, markdown, formatting codes, or markup of any kind.

3. NO ADDITIONAL CONTENT: Return ONLY the rewritten text. Do not add explanations, comments, introductions, conclusions, or any other content beyond the rewritten text itself.

4. MAINTAIN MEANING: Keep the core meaning and information intact while improving clarity and professionalism.

5. THAI LANGUAGE: Ensure proper Thai grammar, spelling, and natural flow.' . $feedback_instructions . $word_guide . $phrase_guide . $alternatives_instruction . $forbidden_instruction . '

Respond with only the plain text rewritten version, nothing else.'
        );

        // Add examples from feedback database
        foreach ($examples as $example) {
            $messages[] = array('role' => 'user', 'content' => $example['original_text']);
            $messages[] = array('role' => 'assistant', 'content' => $example['approved_text']);
        }

        // Add the current text to rewrite
        $messages[] = array('role' => 'user', 'content' => $text);

        // Try each model until one works
        foreach ($possible_models as $model) {
            // Simple payload matching the documentation example
            $payload = array(
                'model' => $model,
                'messages' => $messages
            );

            // Log the request for debugging
            error_log('Thai AI Rewriter - Trying model: ' . $model);
            error_log('Thai AI Rewriter - Request Payload: ' . json_encode($payload, JSON_UNESCAPED_UNICODE));

            $response = wp_remote_post('https://api.opentyphoon.ai/v1/chat/completions', array(
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $api_key
                ),
                'body' => json_encode($payload, JSON_UNESCAPED_UNICODE),
                'timeout' => 60
            ));

            if (is_wp_error($response)) {
                error_log('Thai AI Rewriter - WP Error with model ' . $model . ': ' . $response->get_error_message());
                continue; // Try next model
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            // Log the response for debugging
            error_log('Thai AI Rewriter - Model: ' . $model . ' - Response Code: ' . $response_code);
            error_log('Thai AI Rewriter - Response Body: ' . $body);

            if ($response_code === 200) {
                $data = json_decode($body, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    error_log('Thai AI Rewriter - JSON decode error: ' . json_last_error_msg());
                    continue;
                }

                if (isset($data['choices'][0]['message']['content'])) {
                    $content = trim($data['choices'][0]['message']['content']);
                    if (!empty($content)) {
                        // Clean the content: remove HTML tags and apply feedback patterns
                        $cleaned_content = $this->clean_api_response($content, $text, $examples);

                        // Log the rewrite history
                        $this->log_rewrite_history($text, $cleaned_content, $model);

                        error_log('Thai AI Rewriter - Success with model: ' . $model);
                        error_log('Thai AI Rewriter - Original response: ' . $content);
                        error_log('Thai AI Rewriter - Cleaned response: ' . $cleaned_content);

                        return $cleaned_content;
                    }
                }

                error_log('Thai AI Rewriter - Invalid response structure: ' . print_r($data, true));
                continue; // Try next model

            } elseif ($response_code === 400) {
                $data = json_decode($body, true);
                error_log('Thai AI Rewriter - 400 Error with model ' . $model . ': ' . $body);

                // If it's a model error, try next model
                if (isset($data['error']['code']) && $data['error']['code'] === 'model_not_found') {
                    continue;
                }

                // For other 400 errors, return the specific error
                if (isset($data['error']['message'])) {
                    return new WP_Error('api_error', $data['error']['message'] . ' (HTTP 400)');
                }

                continue; // Try next model

            } elseif ($response_code === 401) {
                return new WP_Error('api_error', 'Invalid API key. Please check your API key. (HTTP 401)');

            } elseif ($response_code === 429) {
                return new WP_Error('api_error', 'Rate limit exceeded. Please try again later. (HTTP 429)');

            } else {
                $data = json_decode($body, true);
                $error_msg = isset($data['error']['message']) ? $data['error']['message'] : 'Unknown error';
                error_log('Thai AI Rewriter - HTTP ' . $response_code . ' Error: ' . $error_msg);

                // For 5xx errors, try next model
                if ($response_code >= 500) {
                    continue;
                }

                // For other errors, return immediately
                return new WP_Error('api_error', $error_msg . ' (HTTP ' . $response_code . ')');
            }
        }

        // If we get here, all models failed
        return new WP_Error('api_error', 'All available models failed. Please check the error logs for details.');
    }

    private function clean_api_response($content, $original_text, $examples)
    {
        // 1. Remove any HTML tags
        $content = strip_tags($content);

        // 2. Remove common unwanted prefixes/suffixes that AI might add
        $unwanted_patterns = array(
            '/^(Here\'s the rewritten text:|Rewritten text:|Here is the rewritten version:|The rewritten text is:)/i',
            '/^(ข้อความใหม่:|ข้อความใหม่:|เวอร์สใหม่:)/u',
            '/\n*$/', // Remove trailing newlines
            '/^\s*"(.*?)"\s*$/s', // Remove surrounding quotes
            '/^\s*\'(.*?)\'\s*$/s', // Remove surrounding single quotes
        );

        foreach ($unwanted_patterns as $pattern) {
            $content = preg_replace($pattern, '$1', $content);
        }

        // 3. Clean up extra whitespace
        $content = preg_replace('/\s+/u', ' ', $content);
        $content = trim($content);

        // 4. Apply feedback database patterns for better word choices
        $content = $this->apply_feedback_patterns($content, $examples);

        // 5. Final validation - if content is too different or empty, return original
        if (empty($content) || mb_strlen($content) < (mb_strlen($original_text) * 0.3)) {
            error_log('Thai AI Rewriter - Content too short or empty, returning original');
            return $original_text;
        }

        return $content;
    }

    private function log_rewrite_history($original_text, $rewritten_text, $model_used)
    {
        // Get the rewrite category
        $category = get_term_by('slug', 'rewrite-work-history', 'category');
        $category_id = $category ? $category->term_id : 0;

        // Create post title from first 50 characters of original text
        $post_title = 'Rewrite: ' . mb_substr($original_text, 0, 50) . (mb_strlen($original_text) > 50 ? '...' : '');

        // Create post content with both original and rewritten text
        $post_content = "<h3>Original Text:</h3>\n<div class=\"original-text\">" . wpautop($original_text) . "</div>\n\n";
        $post_content .= "<h3>Rewritten Text:</h3>\n<div class=\"rewritten-text\">" . wpautop($rewritten_text) . "</div>";

        // Create the post
        $post_id = wp_insert_post(array(
            'post_title' => $post_title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'thai_rewrite_work',
            'post_category' => array($category_id),
            'meta_input' => array(
                'original_text' => $original_text,
                'rewritten_text' => $rewritten_text,
                'model_used' => $model_used,
                'user_ip' => $this->get_client_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'word_count_original' => str_word_count($original_text),
                'word_count_rewritten' => str_word_count($rewritten_text),
                'processing_status' => 'completed'
            )
        ));

        // Also log to database table for statistics (keep both systems)
        global $wpdb;
        $table_name = $wpdb->prefix . 'thai_ai_rewrite_history';
        $wpdb->insert(
            $table_name,
            array(
                'original_text' => $original_text,
                'rewritten_text' => $rewritten_text,
                'user_ip' => $this->get_client_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'model_used' => $model_used,
                'word_count_original' => str_word_count($original_text),
                'word_count_rewritten' => str_word_count($rewritten_text),
                'status' => 'completed'
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%d', '%s')
        );

        return $post_id;
    }

    private function log_combined_work_history($original_text, $combined_text)
    {
        // Get the rewrite category
        $category = get_term_by('slug', 'rewrite-work-history', 'category');
        $category_id = $category ? $category->term_id : 0;

        // Create post title from first 50 characters of combined text
        $post_title = 'Combined Rewrite: ' . mb_substr($combined_text, 0, 50) . (mb_strlen($combined_text) > 50 ? '...' : '');

        // Create post content with both original and combined text
        $post_content = "<h3>Original Text:</h3>\n<div class=\"original-text\">" . wpautop($original_text) . "</div>\n\n";
        $post_content .= "<h3>Final Combined Version:</h3>\n<div class=\"combined-text\">" . wpautop($combined_text) . "</div>";

        // Create the post
        $post_id = wp_insert_post(array(
            'post_title' => $post_title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'thai_rewrite_work',
            'post_category' => array($category_id),
            'meta_input' => array(
                'original_text' => $original_text,
                'rewritten_text' => $combined_text,
                'model_used' => 'Combined Version',
                'user_ip' => $this->get_client_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'word_count_original' => str_word_count($original_text),
                'word_count_rewritten' => str_word_count($combined_text),
                'processing_status' => 'completed',
                'work_type' => 'combined'
            )
        ));

        // Also log to database table for statistics
        global $wpdb;
        $table_name = $wpdb->prefix . 'thai_ai_rewrite_history';
        $wpdb->insert(
            $table_name,
            array(
                'original_text' => $original_text,
                'rewritten_text' => $combined_text,
                'user_ip' => $this->get_client_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'model_used' => 'Combined Version',
                'word_count_original' => str_word_count($original_text),
                'word_count_rewritten' => str_word_count($combined_text),
                'status' => 'completed'
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%d', '%s')
        );

        return $post_id;
    }

    private function get_client_ip()
    {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    // Custom columns for Work History post type
    public function add_rewrite_columns($columns)
    {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['original_preview'] = 'Original Text';
        $new_columns['rewritten_preview'] = 'Rewritten Text';
        $new_columns['model_used'] = 'Model';
        $new_columns['word_counts'] = 'Word Count';
        $new_columns['date'] = $columns['date'];

        return $new_columns;
    }

    public function fill_rewrite_columns($column, $post_id)
    {
        switch ($column) {
            case 'original_preview':
                $original = get_post_meta($post_id, 'original_text', true);
                if ($original) {
                    echo '<div style="max-height: 60px; overflow-y: auto; padding: 5px; background: #f9f9f9; border-radius: 3px; font-size: 12px;">';
                    echo esc_html(mb_substr($original, 0, 100)) . (mb_strlen($original) > 100 ? '...' : '');
                    echo '</div>';
                } else {
                    echo '<em>No original text</em>';
                }
                break;

            case 'rewritten_preview':
                $rewritten = get_post_meta($post_id, 'rewritten_text', true);
                if ($rewritten) {
                    echo '<div style="max-height: 60px; overflow-y: auto; padding: 5px; background: #f0f8ff; border-radius: 3px; font-size: 12px;">';
                    echo esc_html(mb_substr($rewritten, 0, 100)) . (mb_strlen($rewritten) > 100 ? '...' : '');
                    echo '</div>';
                } else {
                    echo '<em>No rewritten text</em>';
                }
                break;

            case 'model_used':
                $model = get_post_meta($post_id, 'model_used', true);
                echo $model ? '<small>' . esc_html($model) . '</small>' : '<em>Unknown</em>';
                break;

            case 'word_counts':
                $original_count = get_post_meta($post_id, 'word_count_original', true);
                $rewritten_count = get_post_meta($post_id, 'word_count_rewritten', true);
                if ($original_count && $rewritten_count) {
                    echo '<small>' . $original_count . ' → ' . $rewritten_count . '</small>';
                } else {
                    echo '<em>N/A</em>';
                }
                break;
        }
    }

    // Word Alternatives Management
    public function handle_save_word_alternatives()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $original_word = sanitize_text_field($_POST['original_word']);
        $alternative_words = sanitize_textarea_field($_POST['alternative_words']);
        $description = sanitize_textarea_field($_POST['description']);

        if (empty($original_word) || empty($alternative_words)) {
            wp_send_json_error('กรอกคำและคำทาง');
        }

        $table_name = $wpdb->prefix . 'thai_ai_word_alternatives';

        $result = $wpdb->replace(
            $table_name,
            array(
                'original_word' => $original_word,
                'alternative_words' => $alternative_words,
                'description' => $description,
                'is_active' => 1
            ),
            array('%s', '%s', '%s', '%d')
        );

        if ($result === false) {
            wp_send_json_error('ไม่สามารถบันทึกข้อมูลได้');
        } else {
            wp_send_json_success('บันทึกคำทางสำเร็จ');
        }
    }

    public function handle_get_word_alternatives()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_word_alternatives';

        $results = $wpdb->get_results(
            "SELECT * FROM $table_name WHERE is_active = 1 ORDER BY updated_at DESC",
            ARRAY_A
        );

        wp_send_json_success($results);
    }

    // Forbidden Words Management
    public function handle_save_forbidden_words()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $forbidden_word = sanitize_text_field($_POST['forbidden_word']);
        $reason = sanitize_textarea_field($_POST['reason']);

        if (empty($forbidden_word)) {
            wp_send_json_error('กรอกคำต้องห้าม');
        }

        $table_name = $wpdb->prefix . 'thai_ai_forbidden_words';

        $result = $wpdb->replace(
            $table_name,
            array(
                'forbidden_word' => $forbidden_word,
                'reason' => $reason,
                'is_active' => 1
            ),
            array('%s', '%s', '%d')
        );

        if ($result === false) {
            wp_send_json_error('ไม่สามารถบันทึกข้อมูลได้');
        } else {
            wp_send_json_success('บันทึกคำต้องห้ามสำเร็จ');
        }
    }

    public function handle_get_forbidden_words()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_forbidden_words';

        $results = $wpdb->get_results(
            "SELECT * FROM $table_name WHERE is_active = 1 ORDER BY created_at DESC",
            ARRAY_A
        );

        wp_send_json_success($results);
    }

    private function get_word_alternatives()
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_word_alternatives';

        $results = $wpdb->get_results(
            "SELECT original_word, alternative_words FROM $table_name WHERE is_active = 1",
            ARRAY_A
        );

        $alternatives = array();
        foreach ($results as $row) {
            $alternatives[$row['original_word']] = explode('|', $row['alternative_words']);
        }

        return $alternatives;
    }

    private function get_forbidden_words()
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_forbidden_words';

        $results = $wpdb->get_results(
            "SELECT forbidden_word FROM $table_name WHERE is_active = 1",
            ARRAY_A
        );

        return array_column($results, 'forbidden_word');
    }

    // AI Instructions Management
    public function handle_save_instructions()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $instruction_type = sanitize_text_field($_POST['instruction_type']);
        $instruction_content = sanitize_textarea_field($_POST['instruction_content']);

        if (empty($instruction_type) || empty($instruction_content)) {
            wp_send_json_error('กรอกข้อมูลให้ครบถ้วน');
        }

        $table_name = $wpdb->prefix . 'thai_ai_instructions';

        $result = $wpdb->replace(
            $table_name,
            array(
                'instruction_type' => $instruction_type,
                'instruction_content' => $instruction_content,
                'is_active' => 1
            ),
            array('%s', '%s', '%d')
        );

        if ($result === false) {
            wp_send_json_error('ไม่สามารถบันทึกข้อมูลได้');
        } else {
            wp_send_json_success('บันทึกคำแนะนำสำเร็จ');
        }
    }

    public function handle_get_instructions()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_instructions';

        $results = $wpdb->get_results(
            "SELECT * FROM $table_name WHERE is_active = 1 ORDER BY instruction_type",
            ARRAY_A
        );

        $instructions = array();
        foreach ($results as $row) {
            $instructions[$row['instruction_type']] = $row['instruction_content'];
        }

        wp_send_json_success($instructions);
    }

    private function get_stored_instructions()
    {
        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_instructions';

        $results = $wpdb->get_results(
            "SELECT instruction_type, instruction_content FROM $table_name WHERE is_active = 1",
            ARRAY_A
        );

        $instructions = array();
        foreach ($results as $row) {
            $instructions[$row['instruction_type']] = $row['instruction_content'];
        }

        return $instructions;
    }

    // History Management
    public function handle_get_history_stats()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_rewrite_history';

        $stats = $wpdb->get_row(
            "SELECT 
                COUNT(*) as total_rewrites,
                COUNT(DISTINCT DATE(created_at)) as active_days,
                SUM(word_count_original) as total_original_words,
                SUM(word_count_rewritten) as total_rewritten_words,
                AVG(word_count_original) as avg_original_words,
                AVG(word_count_rewritten) as avg_rewritten_words,
                MAX(created_at) as last_rewrite
            FROM $table_name",
            ARRAY_A
        );

        wp_send_json_success($stats);
    }

    public function handle_clear_history()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_rewrite_history';

        $result = $wpdb->query("TRUNCATE TABLE $table_name");

        if ($result === false) {
            wp_send_json_error('Failed to clear history');
        } else {
            wp_send_json_success('History cleared successfully');
        }
    }

    public function handle_save_combined_work()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        $original_text = sanitize_textarea_field($_POST['original_text'] ?? '');
        $combined_text = sanitize_textarea_field($_POST['combined_text'] ?? '');

        if (empty($original_text) || empty($combined_text)) {
            wp_send_json_error('Original text and combined text are required');
            return;
        }

        try {
            // Save as work history post
            $post_id = $this->log_combined_work_history($original_text, $combined_text);

            if ($post_id) {
                wp_send_json_success(array(
                    'message' => 'Combined work saved successfully',
                    'post_id' => $post_id
                ));
            } else {
                wp_send_json_error('Failed to save combined work');
            }
        } catch (Exception $e) {
            error_log('Thai AI Rewriter - Error saving combined work: ' . $e->getMessage());
            wp_send_json_error('Error saving combined work: ' . $e->getMessage());
        }
    }

    private function build_stored_word_alternatives_instruction()
    {
        $word_alternatives = $this->get_word_alternatives();

        if (empty($word_alternatives)) {
            return '';
        }

        $instruction = "9. WORD ALTERNATIVES: When you encounter these words, randomly choose from the alternatives provided:\n";
        foreach ($word_alternatives as $original => $options) {
            $instruction .= "- \"$original\" → choose randomly from: " . implode(', ', array_map(function ($opt) {
                return "\"$opt\"";
            }, $options)) . "\n";
        }

        // Store in database
        global $wpdb;
        $table_name = $wpdb->prefix . 'thai_ai_instructions';
        $wpdb->replace(
            $table_name,
            array(
                'instruction_type' => 'word_alternatives',
                'instruction_content' => $instruction,
                'is_active' => 1
            ),
            array('%s', '%s', '%d')
        );

        return $instruction;
    }

    private function build_stored_forbidden_words_instruction()
    {
        $forbidden_words = $this->get_forbidden_words();

        if (empty($forbidden_words)) {
            return '';
        }

        $instruction = "10. FORBIDDEN WORDS: NEVER use these words in your rewrite:\n";
        foreach ($forbidden_words as $forbidden) {
            $instruction .= "- \"$forbidden\" (ห้ามใช้)\n";
        }

        // Store in database
        global $wpdb;
        $table_name = $wpdb->prefix . 'thai_ai_instructions';
        $wpdb->replace(
            $table_name,
            array(
                'instruction_type' => 'forbidden_words',
                'instruction_content' => $instruction,
                'is_active' => 1
            ),
            array('%s', '%s', '%d')
        );

        return $instruction;
    }

    private function apply_word_alternatives($text, $alternatives)
    {
        foreach ($alternatives as $original => $options) {
            if (strpos($text, $original) !== false) {
                // Random select from alternatives
                $selected = $options[array_rand($options)];
                $text = str_replace($original, $selected, $text);
            }
        }

        return $text;
    }

    private function build_word_recommendations($word_patterns)
    {
        $recommendations = array();

        foreach ($word_patterns as $original_word => $replacements) {
            // Find the most frequently used replacement
            arsort($replacements);
            $best_replacement = array_key_first($replacements);
            $frequency = $replacements[$best_replacement];

            // Only include if it appears multiple times (shows consistency)
            if ($frequency >= 2) {
                $recommendations[] = "- \"$original_word\" → \"$best_replacement\" (used $frequency times)";
            }
        }

        return implode("\n", array_slice($recommendations, 0, 10)); // Limit to top 10
    }

    private function build_phrase_recommendations($phrase_patterns)
    {
        $recommendations = array();

        foreach ($phrase_patterns as $original_phrase => $replacements) {
            // Find the most frequently used replacement
            arsort($replacements);
            $best_replacement = array_key_first($replacements);
            $frequency = $replacements[$best_replacement];

            // Only include if it appears multiple times (shows consistency)
            if ($frequency >= 2) {
                $recommendations[] = "- \"$original_phrase\" → \"$best_replacement\" (used $frequency times)";
            }
        }

        return implode("\n", array_slice($recommendations, 0, 8)); // Limit to top 8
    }

    public function handle_get_feedback()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_feedback';

        $results = $wpdb->get_results(
            "SELECT original_text, approved_text, created_at FROM $table_name ORDER BY created_at DESC",
            ARRAY_A
        );

        wp_send_json_success($results);
    }

    public function handle_clear_feedback()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        global $wpdb;

        $table_name = $wpdb->prefix . 'thai_ai_feedback';

        $result = $wpdb->query("TRUNCATE TABLE $table_name");

        if ($result === false) {
            wp_send_json_error('Failed to clear database');
        } else {
            wp_send_json_success('Database cleared successfully');
        }
    }

    public function handle_csv_upload()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error('File upload failed');
        }

        $file = $_FILES['csv_file'];
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);

        if (strtolower($file_extension) !== 'csv') {
            wp_send_json_error('Please upload a CSV file');
        }

        $csv_content = file_get_contents($file['tmp_name']);
        if ($csv_content === false) {
            wp_send_json_error('Failed to read CSV file');
        }

        // Parse CSV to validate format
        $rows = $this->parse_csv($csv_content);
        if (empty($rows)) {
            wp_send_json_error('CSV file is empty or invalid');
        }

        // Generate session ID
        $session_id = uniqid('csv_', true);

        // Store in database
        global $wpdb;
        $table_name = $wpdb->prefix . 'thai_ai_csv_sessions';

        $result = $wpdb->insert(
            $table_name,
            array(
                'session_id' => $session_id,
                'filename' => sanitize_file_name($file['name']),
                'csv_data' => $csv_content,
                'status' => 'uploaded'
            ),
            array('%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            wp_send_json_error('Failed to save CSV data');
        }

        wp_send_json_success(array(
            'session_id' => $session_id,
            'filename' => $file['name'],
            'row_count' => count($rows),
            'preview' => array_slice($rows, 0, 5) // First 5 rows for preview
        ));
    }

    public function handle_csv_process()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        $session_id = sanitize_text_field($_POST['session_id']);
        $text_column = intval($_POST['text_column']);

        // Use hardcoded API key
        $api_key = 'sk-7VtaggCJcchQOsLgd7fkab6d8Y8GqQLYjpfyjU4BHMenMhTL';

        if (empty($session_id)) {
            wp_send_json_error('Missing session ID');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'thai_ai_csv_sessions';

        $session = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE session_id = %s",
            $session_id
        ));

        if (!$session) {
            wp_send_json_error('CSV session not found');
        }

        $rows = $this->parse_csv($session->csv_data);
        if (empty($rows)) {
            wp_send_json_error('Invalid CSV data');
        }

        $header = array_shift($rows);
        if ($text_column >= count($header)) {
            wp_send_json_error('Invalid text column index');
        }

        // Add new column for AI rewritten text
        $header[] = 'AI_Rewritten';
        $processed_rows = array($header);

        $examples = $this->get_feedback_examples(3);

        foreach ($rows as $row) {
            if (isset($row[$text_column]) && !empty(trim($row[$text_column]))) {
                $original_text = trim($row[$text_column]);
                $rewritten_text = $this->call_typhoon_api($original_text, $api_key, $examples);

                if (is_wp_error($rewritten_text)) {
                    $row[] = 'Error: ' . $rewritten_text->get_error_message();
                } else {
                    $row[] = $rewritten_text;
                }
            } else {
                $row[] = '';
            }
            $processed_rows[] = $row;
        }

        // Convert back to CSV
        $processed_csv = $this->array_to_csv($processed_rows);

        // Update database
        $result = $wpdb->update(
            $table_name,
            array(
                'processed_data' => $processed_csv,
                'status' => 'processed'
            ),
            array('session_id' => $session_id),
            array('%s', '%s'),
            array('%s')
        );

        if ($result === false) {
            wp_send_json_error('Failed to save processed data');
        }

        wp_send_json_success(array(
            'message' => 'CSV processed successfully',
            'processed_rows' => count($processed_rows) - 1 // Exclude header
        ));
    }

    public function handle_csv_download()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        $session_id = sanitize_text_field($_GET['session_id']);

        if (empty($session_id)) {
            wp_die('Missing session ID');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'thai_ai_csv_sessions';

        $session = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE session_id = %s",
            $session_id
        ));

        if (!$session || empty($session->processed_data)) {
            wp_die('Processed CSV not found');
        }

        $filename = 'rewritten_' . $session->filename;

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo $session->processed_data;
        exit;
    }

    private function parse_csv($csv_content)
    {
        $rows = array();
        $lines = explode("\n", $csv_content);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line))
                continue;

            $row = str_getcsv($line);
            $rows[] = $row;
        }

        return $rows;
    }

    private function array_to_csv($array)
    {
        $output = '';
        foreach ($array as $row) {
            $escaped_row = array();
            foreach ($row as $field) {
                // Escape quotes and wrap in quotes if necessary
                if (strpos($field, '"') !== false || strpos($field, ',') !== false || strpos($field, "\n") !== false) {
                    $field = '"' . str_replace('"', '""', $field) . '"';
                }
                $escaped_row[] = $field;
            }
            $output .= implode(',', $escaped_row) . "\n";
        }
        return $output;
    }

    public function handle_save_api_key()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        $api_key = sanitize_text_field($_POST['api_key']);

        if (empty($api_key)) {
            wp_send_json_error('API key cannot be empty');
        }

        // Validate API key format (basic check)
        if (!preg_match('/^sk-[a-zA-Z0-9\-_]+$/', $api_key)) {
            wp_send_json_error('Invalid API key format. Should start with "sk-"');
        }

        $result = update_option('thai_ai_rewriter_api_key', $api_key);

        if ($result) {
            wp_send_json_success('API key saved successfully');
        } else {
            wp_send_json_error('Failed to save API key');
        }
    }

    public function handle_get_api_key()
    {
        check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

        $api_key = get_option('thai_ai_rewriter_api_key', '');

        // Return masked version for security
        if (!empty($api_key)) {
            $masked_key = substr($api_key, 0, 12) . str_repeat('*', strlen($api_key) - 16) . substr($api_key, -4);
            wp_send_json_success(array(
                'has_key' => true,
                'masked_key' => $masked_key
            ));
        } else {
            wp_send_json_success(array(
                'has_key' => false,
                'masked_key' => ''
            ));
        }
    }

    public function admin_page()
    {
        ?>
        <div class="wrap">
            <h1>Thai AI Rewriter</h1>
            <div
                style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                <h2>How to Use</h2>
                <p><strong>Option 1: Use Shortcode</strong></p>
                <p>Add <code>[thai_ai_rewriter]</code> to any page or post where you want the rewriter interface to appear.</p>

                <p><strong>Option 2: Use Admin Interface</strong></p>
                <p>Use the interface below to rewrite text directly from the admin area.</p>

                <p><strong>Option 3: View Work History</strong></p>
                <p>Go to <a href="<?php echo admin_url('edit.php?post_type=thai_rewrite_work'); ?>">Work History</a> to see all
                    previous rewrite work in the WordPress posts dashboard.</p>

                <hr style="margin: 20px 0;">

                <?php
                // Include the rewriter form template
                include THAI_AI_REWRITER_PLUGIN_PATH . 'templates/rewriter-form.php';
                ?>
            </div>
        </div>
        <?php
    }

    public function feedback_page()
    {
        global $wpdb;

        $feedback_table = $wpdb->prefix . 'thai_ai_feedback';
        $csv_table = $wpdb->prefix . 'thai_ai_csv_sessions';

        $feedback_count = $wpdb->get_var("SELECT COUNT(*) FROM $feedback_table");
        $csv_sessions_count = $wpdb->get_var("SELECT COUNT(*) FROM $csv_table");

        $recent_feedback = $wpdb->get_results(
            "SELECT * FROM $feedback_table ORDER BY created_at DESC LIMIT 10",
            ARRAY_A
        );

        $recent_csv_sessions = $wpdb->get_results(
            "SELECT * FROM $csv_table ORDER BY created_at DESC LIMIT 10",
            ARRAY_A
        );
        ?>
        <div class="wrap thai-ai-rewriter-admin thai-ai-rewriter-feedback">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-feedback"
                    style="font-size: 24px; vertical-align: middle; margin-right: 8px;"></span>
                Feedback Database
            </h1>
            <hr class="wp-header-end">

            <!-- Statistics Cards -->
            <div class="postbox-container" style="width: 100%; margin-top: 20px;">
                <div
                    style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">

                    <!-- Statistics Card -->
                    <div class="postbox">
                        <h2 class="hndle">
                            <span class="dashicons dashicons-chart-bar" style="margin-right: 5px;"></span>
                            Statistics
                        </h2>
                        <div class="inside">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div style="text-align: center; padding: 15px; background: #f0f8ff; border-radius: 8px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #0073aa;">
                                        <?php echo number_format($feedback_count); ?>
                                    </div>
                                    <div style="font-size: 12px; color: #666;">Feedback Entries</div>
                                </div>
                                <div style="text-align: center; padding: 15px; background: #f0fff0; border-radius: 8px;">
                                    <div style="font-size: 24px; font-weight: bold; color: #46b450;">
                                        <?php echo number_format($csv_sessions_count); ?>
                                    </div>
                                    <div style="font-size: 12px; color: #666;">CSV Sessions</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class="postbox">
                        <h2 class="hndle">
                            <span class="dashicons dashicons-admin-tools" style="margin-right: 5px;"></span>
                            Quick Actions
                        </h2>
                        <div class="inside">
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <a href="<?php echo admin_url('admin.php?page=thai-ai-rewriter'); ?>"
                                    class="button button-primary"
                                    style="display: flex; align-items: center; justify-content: center; padding: 10px;">
                                    <span class="dashicons dashicons-edit" style="margin-right: 5px;"></span>
                                    Go to Rewriter
                                </a>
                                <a href="<?php echo admin_url('edit.php?post_type=thai_rewrite_work'); ?>"
                                    class="button button-secondary"
                                    style="display: flex; align-items: center; justify-content: center; padding: 10px;">
                                    <span class="dashicons dashicons-admin-post" style="margin-right: 5px;"></span>
                                    View Work History
                                </a>
                                <div style="text-align: center; margin-top: 10px;">
                                    <strong>Shortcode:</strong><br>
                                    <code
                                        style="background: #f1f1f1; padding: 5px 10px; border-radius: 4px; font-size: 13px;">[thai_ai_rewriter]</code>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Recent Feedback Section -->
            <div class="postbox clearfix" style="margin-top: 20px;">
                <h2 class="hndle">
                    <span class="dashicons dashicons-format-chat" style="margin-right: 5px;"></span>
                    Recent Feedback (Last 10)
                </h2>
                <div class="inside clearfix">
                    <?php if (empty($recent_feedback)): ?>
                        <div class="notice notice-info inline" style="margin: 0;">
                            <p><strong>No feedback entries yet.</strong> Start using the rewriter to build your feedback database!
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="feedback-table-container"
                            style="overflow-x: auto; position: relative; clear: both; display: block; width: 100%; margin: 0; padding: 0;">
                            <table class="wp-list-table widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th style="width: 40%; padding: 12px;">
                                            <span class="dashicons dashicons-text" style="margin-right: 5px;"></span>
                                            Original Text
                                        </th>
                                        <th style="width: 40%; padding: 12px;">
                                            <span class="dashicons dashicons-yes" style="margin-right: 5px;"></span>
                                            Approved Text
                                        </th>
                                        <th style="width: 20%; padding: 12px;">
                                            <span class="dashicons dashicons-calendar-alt" style="margin-right: 5px;"></span>
                                            Date
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_feedback as $entry): ?>
                                        <tr>
                                            <td style="padding: 12px;">
                                                <div
                                                    style="max-height: 80px; overflow-y: auto; padding: 10px; background: #f9f9f9; border-left: 4px solid #ddd; border-radius: 4px; font-size: 13px; line-height: 1.4;">
                                                    <?php echo esc_html(mb_substr($entry['original_text'], 0, 200)) . (mb_strlen($entry['original_text']) > 200 ? '...' : ''); ?>
                                                </div>
                                            </td>
                                            <td style="padding: 12px;">
                                                <div
                                                    style="max-height: 80px; overflow-y: auto; padding: 10px; background: #f0f8ff; border-left: 4px solid #0073aa; border-radius: 4px; font-size: 13px; line-height: 1.4;">
                                                    <?php echo esc_html(mb_substr($entry['approved_text'], 0, 200)) . (mb_strlen($entry['approved_text']) > 200 ? '...' : ''); ?>
                                                </div>
                                            </td>
                                            <td style="padding: 12px; text-align: center;">
                                                <div style="font-size: 12px; color: #666;">
                                                    <?php echo date('M j, Y', strtotime($entry['created_at'])); ?><br>
                                                    <?php echo date('H:i', strtotime($entry['created_at'])); ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent CSV Sessions Section -->
            <div class="postbox" style="margin-top: 20px;">
                <h2 class="hndle">
                    <span class="dashicons dashicons-media-spreadsheet" style="margin-right: 5px;"></span>
                    Recent CSV Sessions (Last 10)
                </h2>
                <div class="inside">
                    <?php if (empty($recent_csv_sessions)): ?>
                        <div class="notice notice-info inline" style="margin: 0;">
                            <p><strong>No CSV sessions yet.</strong> Upload a CSV file to see processing history!</p>
                        </div>
                    <?php else: ?>
                        <div class="csv-table-container"
                            style="overflow-x: auto; position: relative; clear: both; display: block; width: 100%; margin: 0; padding: 0;">
                            <table class="wp-list-table widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th style="width: 30%; padding: 12px;">
                                            <span class="dashicons dashicons-media-document" style="margin-right: 5px;"></span>
                                            Filename
                                        </th>
                                        <th style="width: 20%; padding: 12px;">
                                            <span class="dashicons dashicons-info" style="margin-right: 5px;"></span>
                                            Status
                                        </th>
                                        <th style="width: 25%; padding: 12px;">
                                            <span class="dashicons dashicons-calendar-alt" style="margin-right: 5px;"></span>
                                            Date
                                        </th>
                                        <th style="width: 25%; padding: 12px;">
                                            <span class="dashicons dashicons-admin-tools" style="margin-right: 5px;"></span>
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_csv_sessions as $session): ?>
                                        <tr>
                                            <td style="padding: 12px;">
                                                <strong style="color: #0073aa;"><?php echo esc_html($session['filename']); ?></strong>
                                            </td>
                                            <td style="padding: 12px;">
                                                <?php if ($session['status'] === 'processed'): ?>
                                                    <span
                                                        style="display: inline-flex; align-items: center; padding: 4px 8px; background: #d4edda; color: #155724; border-radius: 4px; font-size: 12px;">
                                                        <span class="dashicons dashicons-yes-alt"
                                                            style="font-size: 14px; margin-right: 4px;"></span>
                                                        Processed
                                                    </span>
                                                <?php else: ?>
                                                    <span
                                                        style="display: inline-flex; align-items: center; padding: 4px 8px; background: #fff3cd; color: #856404; border-radius: 4px; font-size: 12px;">
                                                        <span class="dashicons dashicons-clock"
                                                            style="font-size: 14px; margin-right: 4px;"></span>
                                                        <?php echo esc_html(ucfirst($session['status'])); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td style="padding: 12px; text-align: center;">
                                                <div style="font-size: 12px; color: #666;">
                                                    <?php echo date('M j, Y', strtotime($session['created_at'])); ?><br>
                                                    <?php echo date('H:i', strtotime($session['created_at'])); ?>
                                                </div>
                                            </td>
                                            <td style="padding: 12px; text-align: center;">
                                                <?php if ($session['status'] === 'processed' && !empty($session['processed_data'])): ?>
                                                    <a href="<?php echo admin_url('admin-ajax.php?action=thai_ai_download_csv&session_id=' . urlencode($session['session_id']) . '&nonce=' . wp_create_nonce('thai_ai_rewriter_nonce')); ?>"
                                                        class="button button-small button-primary">
                                                        <span class="dashicons dashicons-download"
                                                            style="font-size: 12px; margin-right: 4px;"></span>
                                                        Download
                                                    </a>
                                                <?php else: ?>
                                                    <span class="button button-small button-disabled">
                                                        <span class="dashicons dashicons-clock"
                                                            style="font-size: 12px; margin-right: 4px;"></span>
                                                        Not Ready
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <style>
            .postbox h2.hndle {
                font-size: 14px;
                padding: 12px;
                margin: 0;
                line-height: 1.4;
                border-bottom: 1px solid #ccd0d4;
                background: #f9f9f9;
            }

            /* Clean CSS for feedback database tables */
            .postbox {
                position: relative !important;
                clear: both !important;
                margin-bottom: 20px !important;
                overflow: visible !important;
                display: block !important;
            }

            .postbox .inside {
                padding: 15px !important;
                overflow: visible !important;
                position: relative !important;
                clear: both !important;
                display: block !important;
                width: 100% !important;
            }

            /* Table container fix */
            .postbox .inside>div[style*="overflow-x: auto"] {
                position: relative !important;
                clear: both !important;
                display: block !important;
                width: 100% !important;
                overflow-x: auto !important;
                overflow-y: visible !important;
                margin: 0 !important;
                padding: 0 !important;
                background: transparent !important;
                border: none !important;
                z-index: auto !important;
                float: none !important;
            }

            /* WordPress table fixes */
            .wp-list-table {
                width: 100% !important;
                clear: both !important;
                margin: 0 !important;
                border-collapse: collapse !important;
                table-layout: fixed !important;
                position: static !important;
                z-index: auto !important;
                float: none !important;
                display: table !important;
                background: white !important;
            }

            .wp-list-table.widefat {
                border: 1px solid #c3c4c7 !important;
                box-shadow: none !important;
                width: 100% !important;
            }

            .wp-list-table.fixed {
                table-layout: fixed !important;
            }

            .wp-list-table th,
            .wp-list-table td {
                position: static !important;
                float: none !important;
                display: table-cell !important;
                vertical-align: top !important;
            }

            .wp-list-table th {
                background: #f9f9f9 !important;
                font-weight: 600 !important;
                border-bottom: 1px solid #c3c4c7 !important;
            }

            .wp-list-table td {
                border-bottom: 1px solid #c3c4c7 !important;
            }

            .notice.inline {
                display: block !important;
                margin: 5px 0 15px !important;
                position: relative !important;
                clear: both !important;
            }

            /* Specific fixes for table containers */
            .feedback-table-container,
            .csv-table-container {
                position: relative !important;
                clear: both !important;
                display: block !important;
                width: 100% !important;
                overflow-x: auto !important;
                overflow-y: visible !important;
                margin: 0 !important;
                padding: 0 !important;
                background: transparent !important;
                border: none !important;
                z-index: auto !important;
                float: none !important;
                contain: layout !important;
            }

            .feedback-table-container .wp-list-table,
            .csv-table-container .wp-list-table {
                position: static !important;
                float: none !important;
                display: table !important;
                width: 100% !important;
                margin: 0 !important;
                background: white !important;
            }

            /* Force normal document flow for all elements */
            .postbox *,
            .postbox .wp-list-table *,
            .postbox .inside * {
                position: static !important;
                float: none !important;
                transform: none !important;
                will-change: auto !important;
            }

            /* Additional containment for problematic elements */
            .thai-ai-rewriter-feedback .postbox {
                contain: layout style !important;
                isolation: isolate !important;
            }

            /* Reset any problematic CSS from other plugins/themes */
            .thai-ai-rewriter-feedback table,
            .thai-ai-rewriter-feedback .wp-list-table,
            .thai-ai-rewriter-feedback .postbox table {
                position: static !important;
                float: none !important;
                transform: none !important;
                z-index: auto !important;
                will-change: auto !important;
                contain: none !important;
                left: auto !important;
                right: auto !important;
                top: auto !important;
                bottom: auto !important;
                margin: 0 !important;
                display: table !important;
            }

            /* Ensure proper stacking context */
            .thai-ai-rewriter-feedback {
                position: relative !important;
                z-index: auto !important;
            }

            /* Force block layout for containers */
            .thai-ai-rewriter-feedback .postbox,
            .thai-ai-rewriter-feedback .postbox .inside {
                display: block !important;
                position: relative !important;
                float: none !important;
            }

            @media (max-width: 768px) {
                .postbox-container {
                    width: 100% !important;
                    margin-right: 0 !important;
                }
            }
        </style>
        <?php
    }



    public function render_shortcode($atts)
    {
        ob_start();
        include THAI_AI_REWRITER_PLUGIN_PATH . 'templates/rewriter-form.php';
        return ob_get_clean();
    }
}

new ThaiAiRewriter();
