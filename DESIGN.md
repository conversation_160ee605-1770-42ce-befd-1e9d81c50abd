# Thai AI Rewriter - Design Document

## 🏗️ System Architecture

### **Architecture Overview**
The Thai AI Rewriter follows a **3-tier architecture** pattern optimized for WordPress plugin development:

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  WordPress Admin UI  │  Frontend Shortcode  │  AJAX Interface │
│  - Admin Pages       │  - Public Forms      │  - API Handlers │
│  - Settings Forms    │  - Result Display    │  - Nonce Security│
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     BUSINESS LOGIC LAYER                    │
├─────────────────────────────────────────────────────────────┤
│  Core Plugin Class   │  API Integration     │  Data Processing │
│  - ThaiAiRewriter    │  - OpenTyphoon API   │  - Text Analysis │
│  - Hook Management   │  - Model Selection   │  - Feedback Loop │
│  - Security Layer    │  - Error Handling    │  - CSV Processing│
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      DATA ACCESS LAYER                      │
├─────────────────────────────────────────────────────────────┤
│  WordPress Database  │  Custom Tables       │  Options API    │
│  - wp_options        │  - thai_ai_feedback  │  - Configuration │
│  - wp_posts          │  - word_alternatives │  - API Keys     │
│  - wp_postmeta       │  - forbidden_words   │  - User Settings │
└─────────────────────────────────────────────────────────────┘
```

---

## 🗄️ Database Design

### **Entity Relationship Diagram**

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   wp_options        │    │ thai_ai_feedback    │    │ thai_ai_rewrite_    │
│                     │    │                     │    │ history             │
├─────────────────────┤    ├─────────────────────┤    ├─────────────────────┤
│ option_name (PK)    │    │ id (PK)             │    │ id (PK)             │
│ option_value        │    │ original_text       │    │ original_text       │
│ autoload            │    │ approved_text       │    │ rewritten_text      │
└─────────────────────┘    │ created_at          │    │ user_ip             │
                           └─────────────────────┘    │ processing_time     │
                                                      │ model_used          │
┌─────────────────────┐    ┌─────────────────────┐    │ created_at          │
│ thai_ai_word_       │    │ thai_ai_forbidden_  │    └─────────────────────┘
│ alternatives        │    │ words               │
├─────────────────────┤    ├─────────────────────┤
│ id (PK)             │    │ id (PK)             │    ┌─────────────────────┐
│ original_word       │    │ forbidden_word      │    │ thai_ai_ai_         │
│ alternative_words   │    │ reason              │    │ instructions        │
│ description         │    │ is_active           │    ├─────────────────────┤
│ is_active           │    │ created_at          │    │ id (PK)             │
│ created_at          │    └─────────────────────┘    │ instruction_type    │
│ updated_at          │                               │ instruction_content │
└─────────────────────┘                               │ is_active           │
                                                      │ created_at          │
                                                      │ updated_at          │
                                                      └─────────────────────┘
```

### **Table Specifications**

#### **wp_options (WordPress Core)**
```sql
-- API Key Storage
option_name: 'thai_ai_rewriter_api_key'
option_value: 'encrypted_api_key_string'
autoload: 'yes'
```

#### **thai_ai_feedback**
```sql
CREATE TABLE thai_ai_feedback (
    id MEDIUMINT(9) NOT NULL AUTO_INCREMENT,
    original_text TEXT NOT NULL,
    approved_text TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_created_at (created_at)
);
```

#### **thai_ai_rewrite_history**
```sql
CREATE TABLE thai_ai_rewrite_history (
    id MEDIUMINT(9) NOT NULL AUTO_INCREMENT,
    original_text LONGTEXT NOT NULL,
    rewritten_text LONGTEXT NOT NULL,
    user_ip VARCHAR(45),
    user_agent TEXT,
    processing_time FLOAT,
    model_used VARCHAR(100),
    word_count_original INT,
    word_count_rewritten INT,
    status VARCHAR(20) DEFAULT 'completed',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
);
```

---

## 🎨 User Interface Design

### **Admin Interface Layout**

```
┌─────────────────────────────────────────────────────────────┐
│                    WORDPRESS ADMIN HEADER                   │
├─────────────────────────────────────────────────────────────┤
│ 🇹🇭 Thai AI Rewriter                                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              1. CONFIGURATION                       │    │
│  │  ┌─────────────────────┐  ┌──────────────────────┐  │    │
│  │  │ OpenTyphoon API Key │  │    [Save Key]        │  │    │
│  │  └─────────────────────┘  └──────────────────────┘  │    │
│  │  ✅ API Key configured: sk-7Vtagg***MhTL           │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              2. INPUT OPTIONS                       │    │
│  │  ┌─────────────────┐ ┌─────────────────────────┐    │    │
│  │  │ Manual Text     │ │ CSV Upload              │    │    │
│  │  └─────────────────┘ └─────────────────────────┘    │    │
│  │  ┌─────────────────────────────────────────────┐    │    │
│  │  │                                             │    │    │
│  │  │  Enter Thai text here...                    │    │    │
│  │  │                                             │    │    │
│  │  └─────────────────────────────────────────────┘    │    │
│  │              [Rewrite Text]                         │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              3. RESULTS & FEEDBACK                  │    │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐    │    │
│  │  │ Original    │ │ AI Rewrite  │ │ Your Edit   │    │    │
│  │  │ Text        │ │             │ │             │    │    │
│  │  └─────────────┘ └─────────────┘ └─────────────┘    │    │
│  │              [Save Feedback]                        │    │
│  └─────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### **Frontend Shortcode Interface**

```
┌─────────────────────────────────────────────────────────────┐
│                    WEBSITE FRONTEND                         │
├─────────────────────────────────────────────────────────────┤
│  [thai_ai_rewriter] Shortcode Output:                      │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │  🇹🇭 Thai AI Text Rewriter                          │    │
│  │                                                     │    │
│  │  ┌─────────────────────────────────────────────┐    │    │
│  │  │                                             │    │    │
│  │  │  Enter your Thai text here...               │    │    │
│  │  │                                             │    │    │
│  │  └─────────────────────────────────────────────┘    │    │
│  │                                                     │    │
│  │              [Rewrite Text]                         │    │
│  │                                                     │    │
│  │  ┌─────────────────────────────────────────────┐    │    │
│  │  │ Results will appear here...                 │    │    │
│  │  └─────────────────────────────────────────────┘    │    │
│  └─────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔧 Component Design

### **Core Plugin Class Structure**

```php
class ThaiAiRewriter {
    // ┌─────────────────────────────────────────────────────────┐
    // │                  INITIALIZATION                         │
    // └─────────────────────────────────────────────────────────┘
    public function __construct()
    public function init()
    
    // ┌─────────────────────────────────────────────────────────┐
    // │                 WORDPRESS INTEGRATION                   │
    // └─────────────────────────────────────────────────────────┘
    public function add_admin_menu()
    public function enqueue_scripts()
    public function render_shortcode()
    
    // ┌─────────────────────────────────────────────────────────┐
    // │                   DATABASE MANAGEMENT                   │
    // └─────────────────────────────────────────────────────────┘
    private function create_database_table()
    private function register_post_types()
    
    // ┌─────────────────────────────────────────────────────────┐
    // │                    AJAX HANDLERS                        │
    // └─────────────────────────────────────────────────────────┘
    public function handle_rewrite_request()
    public function handle_save_feedback()
    public function handle_save_api_key()
    public function handle_get_api_key()
    
    // ┌─────────────────────────────────────────────────────────┐
    // │                   AI INTEGRATION                        │
    // └─────────────────────────────────────────────────────────┘
    private function call_typhoon_api()
    private function analyze_feedback_patterns()
    private function apply_feedback_patterns()
    
    // ┌─────────────────────────────────────────────────────────┐
    // │                  UTILITY FUNCTIONS                      │
    // └─────────────────────────────────────────────────────────┘
    private function clean_api_response()
    private function log_rewrite_history()
    private function extract_thai_words()
}
```

### **JavaScript Module Structure**

```javascript
// ┌─────────────────────────────────────────────────────────┐
// │                  INITIALIZATION                         │
// └─────────────────────────────────────────────────────────┘
$(document).ready(function() {
    loadApiKeyFromDatabase();
    initializeEventHandlers();
});

// ┌─────────────────────────────────────────────────────────┐
// │                 API KEY MANAGEMENT                      │
// └─────────────────────────────────────────────────────────┘
function loadApiKeyFromDatabase()
function saveApiKeyToDatabase()
function validateApiKey()

// ┌─────────────────────────────────────────────────────────┐
// │                  TEXT PROCESSING                        │
// └─────────────────────────────────────────────────────────┘
function handleRewriteRequest()
function displayResults()
function handleFeedbackSave()

// ┌─────────────────────────────────────────────────────────┐
// │                   CSV PROCESSING                        │
// └─────────────────────────────────────────────────────────┘
function handleCsvUpload()
function processCsvData()
function downloadProcessedCsv()

// ┌─────────────────────────────────────────────────────────┐
// │                  UI INTERACTIONS                        │
// └─────────────────────────────────────────────────────────┘
function updateButtonStates()
function showLoadingIndicator()
function displayErrorMessage()
```

---

## 🔄 Data Flow Design

### **Text Rewriting Flow**

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   User      │    │  WordPress  │    │ OpenTyphoon │    │  Database   │
│  Interface  │    │   Backend   │    │     API     │    │   Storage   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
   1.  │ Enter Text        │                   │                   │
       │ Click Rewrite     │                   │                   │
       ├──────────────────►│                   │                   │
   2.  │                   │ Get API Key       │                   │
       │                   ├──────────────────────────────────────►│
   3.  │                   │ API Key           │                   │
       │                   │◄──────────────────────────────────────┤
   4.  │                   │ Get Feedback      │                   │
       │                   │ Examples          │                   │
       │                   ├──────────────────────────────────────►│
   5.  │                   │ Examples          │                   │
       │                   │◄──────────────────────────────────────┤
   6.  │                   │ API Request       │                   │
       │                   ├──────────────────►│                   │
   7.  │                   │ AI Response       │                   │
       │                   │◄──────────────────┤                   │
   8.  │                   │ Log History       │                   │
       │                   ├──────────────────────────────────────►│
   9.  │ Display Result    │                   │                   │
       │◄──────────────────┤                   │                   │
  10.  │ Edit & Approve    │                   │                   │
       ├──────────────────►│                   │                   │
  11.  │                   │ Save Feedback     │                   │
       │                   ├──────────────────────────────────────►│
```

### **API Key Management Flow**

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   User      │    │  WordPress  │    │  Database   │
│  Interface  │    │   Backend   │    │   Storage   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
   1.  │ Enter API Key     │                   │
       │ Click Save        │                   │
       ├──────────────────►│                   │
   2.  │                   │ Validate Format   │
       │                   │ (sk-* pattern)    │
   3.  │                   │ Store Encrypted   │
       │                   ├──────────────────►│
   4.  │                   │ Confirmation      │
       │                   │◄──────────────────┤
   5.  │ Success Message   │                   │
       │◄──────────────────┤                   │
   6.  │ Page Reload       │                   │
       ├──────────────────►│                   │
   7.  │                   │ Get Masked Key    │
       │                   ├──────────────────►│
   8.  │                   │ Masked Display    │
       │                   │◄──────────────────┤
   9.  │ Show Status       │                   │
       │◄──────────────────┤                   │
```

---

## 🔐 Security Design

### **Security Layers**

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION SECURITY                    │
├─────────────────────────────────────────────────────────────┤
│ • WordPress Nonce Verification                              │
│ • CSRF Protection                                           │
│ • Input Validation & Sanitization                          │
│ • XSS Prevention                                            │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION SECURITY                      │
├─────────────────────────────────────────────────────────────┤
│ • Capability Checks (manage_options)                       │
│ • API Key Format Validation                                │
│ • Rate Limiting Implementation                              │
│ • Error Message Sanitization                               │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     DATA SECURITY                           │
├─────────────────────────────────────────────────────────────┤
│ • API Key Encryption in Database                           │
│ • SQL Injection Prevention (Prepared Statements)           │
│ • Data Sanitization Before Storage                         │
│ • Secure Database Table Creation                           │
└─────────────────────────────────────────────────────────────┘
```

### **Security Implementation**

```php
// Nonce Verification
check_ajax_referer('thai_ai_rewriter_nonce', 'nonce');

// Input Sanitization
$text = sanitize_textarea_field($_POST['text']);
$api_key = sanitize_text_field($_POST['api_key']);

// Capability Check
if (!current_user_can('manage_options')) {
    wp_send_json_error('Insufficient permissions');
}

// API Key Validation
if (!preg_match('/^sk-[a-zA-Z0-9\-_]+$/', $api_key)) {
    wp_send_json_error('Invalid API key format');
}

// Prepared Statements
$wpdb->prepare("INSERT INTO {$table} (text) VALUES (%s)", $text);
```

---

## 🚀 Performance Design

### **Optimization Strategies**

#### **Frontend Performance**
```javascript
// Lazy Loading
$(document).ready(function() {
    // Load only essential components first
    loadCriticalComponents();
    
    // Load secondary features on demand
    $('.advanced-features').on('click', loadAdvancedFeatures);
});

// Debounced Input
const debouncedSearch = debounce(function(query) {
    performSearch(query);
}, 300);

// Efficient DOM Updates
function updateResults(data) {
    const fragment = document.createDocumentFragment();
    data.forEach(item => {
        const element = createResultElement(item);
        fragment.appendChild(element);
    });
    resultsContainer.appendChild(fragment);
}
```

#### **Backend Performance**
```php
// Database Query Optimization
private function get_feedback_examples($limit = 5) {
    global $wpdb;
    
    // Use indexed columns and LIMIT
    return $wpdb->get_results($wpdb->prepare("
        SELECT original_text, approved_text 
        FROM {$this->feedback_table} 
        ORDER BY created_at DESC 
        LIMIT %d
    ", $limit));
}

// Caching Strategy
private function get_cached_instructions() {
    $cache_key = 'thai_ai_instructions_' . md5('current_version');
    $instructions = wp_cache_get($cache_key);
    
    if (false === $instructions) {
        $instructions = $this->build_instructions();
        wp_cache_set($cache_key, $instructions, '', 3600);
    }
    
    return $instructions;
}
```

### **Resource Management**

```
┌─────────────────────────────────────────────────────────────┐
│                    MEMORY MANAGEMENT                        │
├─────────────────────────────────────────────────────────────┤
│ • Limit CSV processing to 1000 rows per batch              │
│ • Use streaming for large file processing                  │
│ • Implement garbage collection for long operations         │
│ • Monitor memory usage during API calls                    │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                   DATABASE OPTIMIZATION                     │
├─────────────────────────────────────────────────────────────┤
│ • Index frequently queried columns                         │
│ • Use prepared statements for all queries                  │
│ • Implement query result caching                           │
│ • Regular database cleanup for old records                 │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                     API OPTIMIZATION                        │
├─────────────────────────────────────────────────────────────┤
│ • Implement exponential backoff for retries               │
│ • Use connection pooling for multiple requests            │
│ • Compress request/response data                          │
│ • Monitor and respect rate limits                         │
└─────────────────────────────────────────────────────────────┘
```

---

## 🧪 Testing Design

### **Testing Strategy**

```
┌─────────────────────────────────────────────────────────────┐
│                      UNIT TESTING                           │
├─────────────────────────────────────────────────────────────┤
│ • PHP Functions (PHPUnit)                                  │
│ • JavaScript Functions (Jest)                              │
│ • Database Operations                                       │
│ • API Integration Methods                                   │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                   INTEGRATION TESTING                       │
├─────────────────────────────────────────────────────────────┤
│ • WordPress Plugin Integration                             │
│ • OpenTyphoon API Connectivity                            │
│ • Database Schema Validation                               │
│ • AJAX Request/Response Cycles                            │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                    SYSTEM TESTING                           │
├─────────────────────────────────────────────────────────────┤
│ • End-to-End User Workflows                               │
│ • Cross-Browser Compatibility                             │
│ • Performance Under Load                                   │
│ • Security Vulnerability Assessment                       │
└─────────────────────────────────────────────────────────────┘
```

### **Test Cases Structure**

```php
// Example Unit Test
class TestThaiAiRewriter extends WP_UnitTestCase {
    
    public function test_api_key_validation() {
        $plugin = new ThaiAiRewriter();
        
        // Valid API key
        $this->assertTrue($plugin->validate_api_key('sk-valid123key'));
        
        // Invalid API key
        $this->assertFalse($plugin->validate_api_key('invalid-key'));
    }
    
    public function test_text_sanitization() {
        $plugin = new ThaiAiRewriter();
        $dirty_text = '<script>alert("xss")</script>Hello';
        $clean_text = $plugin->sanitize_input($dirty_text);
        
        $this->assertEquals('Hello', $clean_text);
    }
}
```

---

## 📦 Deployment Design

### **Deployment Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                   DEVELOPMENT ENVIRONMENT                   │
├─────────────────────────────────────────────────────────────┤
│ • Local WordPress Installation                             │
│ • Development API Keys                                     │
│ • Debug Mode Enabled                                       │
│ • Test Database                                            │
└─────────────────────────────────────────────────────────────┘
                                │
                         [Build Process]
                                │
┌─────────────────────────────────────────────────────────────┐
│                    STAGING ENVIRONMENT                      │
├─────────────────────────────────────────────────────────────┤
│ • Production-like WordPress Setup                         │
│ • Staging API Keys                                         │
│ • Performance Testing                                      │
│ • User Acceptance Testing                                  │
└─────────────────────────────────────────────────────────────┘
                                │
                        [Release Process]
                                │
┌─────────────────────────────────────────────────────────────┐
│                   PRODUCTION ENVIRONMENT                    │
├─────────────────────────────────────────────────────────────┤
│ • Live WordPress Sites                                     │
│ • Production API Keys                                      │
│ • Monitoring & Logging                                     │
│ • Backup & Recovery                                        │
└─────────────────────────────────────────────────────────────┘
```

### **Release Process**

```
1. Code Review & Testing
   ├── Unit Tests Pass
   ├── Integration Tests Pass
   ├── Security Scan Clean
   └── Performance Benchmarks Met

2. Build & Package
   ├── Minify CSS/JavaScript
   ├── Optimize Images
   ├── Generate Documentation
   └── Create Plugin ZIP

3. Staging Deployment
   ├── Deploy to Staging
   ├── Run Automated Tests
   ├── Manual QA Testing
   └── Stakeholder Approval

4. Production Release
   ├── Database Migration Scripts
   ├── Plugin File Deployment
   ├── Configuration Updates
   └── Post-deployment Verification

5. Monitoring & Support
   ├── Error Rate Monitoring
   ├── Performance Metrics
   ├── User Feedback Collection
   └── Issue Resolution
```

---

## 📊 Monitoring Design

### **Monitoring Strategy**

```php
// Performance Monitoring
class PerformanceMonitor {
    public function log_api_call($start_time, $end_time, $model) {
        $duration = $end_time - $start_time;
        
        error_log("Thai AI Rewriter - API Call Performance: {$duration}s using {$model}");
        
        // Store in database for analysis
        $this->store_performance_metric('api_call', $duration, $model);
    }
    
    public function monitor_memory_usage() {
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        
        if ($memory_usage > 50 * 1024 * 1024) { // 50MB threshold
            error_log("Thai AI Rewriter - High memory usage: " . 
                     number_format($memory_usage / 1024 / 1024, 2) . "MB");
        }
    }
}
```

### **Health Checks**

```javascript
// Frontend Health Check
function performHealthCheck() {
    const checks = [
        checkApiKeyStatus(),
        checkDatabaseConnection(),
        checkExternalApiAccess(),
        checkJavaScriptErrors()
    ];
    
    Promise.all(checks).then(results => {
        const healthStatus = results.every(check => check.status === 'ok');
        reportHealthStatus(healthStatus, results);
    });
}
```

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Next Review**: March 2025  
**Architecture Review**: June 2025