
console.log('=== THAI AI REWRITER DEBUG START ===');
console.log('jQuery available:', typeof jQuery);

jQuery(document).ready(function($) {
    console.log('Document ready fired');
    console.log('thaiAiRewriter available:', typeof thaiAiRewriter);
    
    if (typeof thaiAiRewriter !== 'undefined') {
        console.log('thaiAiRewriter.ajax_url:', thaiAiRewriter.ajax_url);
        console.log('thaiAiRewriter.nonce:', thaiAiRewriter.nonce);
    } else {
        console.error('thaiAiRewriter object is missing!');
    }
    
    let hasApiKey = false;
    
    // DOM Elements
    const $apiKeyInput = $('#api-key');
    const $saveKeyBtn = $('#save-key-btn');
    const $apiStatus = $('#api-status');
    const $rewriteBtn = $('#rewrite-btn');
    const $originalTextInput = $('#original-text-input');
    const $resultsContainer = $('#results-container');
    const $placeholderResults = $('#placeholder-results');
    
    // CSV Elements
    const $csvFileInput = $('#csv-file-input');
    const $csvUploadArea = $('#csv-upload-area');
    const $processCsvBtn = $('#process-csv-btn');
    const $csvResults = $('#csv-results');

    // Tab Elements
    const $textTab = $('#text-tab');
    const $csvTab = $('#csv-tab');
    const $textInputSection = $('#text-input-section');
    const $csvUploadSection = $('#csv-upload-section');
    
    // Test if elements exist
    console.log('Save button exists:', $saveKeyBtn.length);
    console.log('API input exists:', $apiKeyInput.length);
    console.log('CSV file input exists:', $csvFileInput.length);
    console.log('CSV upload area exists:', $csvUploadArea.length);
    console.log('CSV process button exists:', $processCsvBtn.length);
    
    // Load API key status on page load
    loadApiKeyFromDatabase();
    
    // Event Handlers
    $saveKeyBtn.on('click', function(e) {
        e.preventDefault();
        console.log('Save button clicked!');
        saveApiKeyToDatabase();
    });
    
    $rewriteBtn.on('click', function(e) {
        e.preventDefault();
        console.log('Rewrite button clicked!');
        handleRewrite();
    });
    
    // Handle CSV file selection
    $csvUploadArea.on('click', function(e) {
        e.preventDefault();
        console.log('CSV upload area clicked!');
        console.log('CSV file input element:', $csvFileInput[0]);
        console.log('CSV file input length:', $csvFileInput.length);

        if ($csvFileInput.length > 0) {
            console.log('Triggering file input click...');
            $csvFileInput[0].click(); // Use native click instead of jQuery
        } else {
            console.error('CSV file input not found!');
        }
    });
    
    $csvFileInput.on('change', function(e) {
        console.log('CSV file selected!');
        handleCsvUpload();
    });
    
    $processCsvBtn.on('click', function(e) {
        e.preventDefault();
        console.log('CSV Process button clicked!');
        handleCsvProcess();
    });

    // Tab switching handlers
    $textTab.on('click', function(e) {
        e.preventDefault();
        switchToTab('text');
    });

    $csvTab.on('click', function(e) {
        e.preventDefault();
        switchToTab('csv');
    });
    
    // Functions
    function switchToTab(tabName) {
        console.log('Switching to tab:', tabName);

        if (tabName === 'text') {
            // Activate text tab
            $textTab.addClass('active text-blue-600 border-blue-600').removeClass('text-gray-500');
            $csvTab.removeClass('active text-blue-600 border-blue-600').addClass('text-gray-500');

            // Show text section, hide CSV section
            $textInputSection.removeClass('hidden').addClass('tab-content');
            $csvUploadSection.addClass('hidden').removeClass('tab-content');
        } else if (tabName === 'csv') {
            // Activate CSV tab
            $csvTab.addClass('active text-blue-600 border-blue-600').removeClass('text-gray-500');
            $textTab.removeClass('active text-blue-600 border-blue-600').addClass('text-gray-500');

            // Show CSV section, hide text section
            $csvUploadSection.removeClass('hidden').addClass('tab-content');
            $textInputSection.addClass('hidden').removeClass('tab-content');
        }
    }

    function loadApiKeyFromDatabase() {
        console.log('Loading API key status...');
        
        if (typeof thaiAiRewriter === 'undefined') {
            console.error('Cannot load API key - thaiAiRewriter object missing');
            return;
        }
        
        $.ajax({
            url: thaiAiRewriter.ajax_url,
            type: 'POST',
            data: {
                action: 'thai_ai_get_api_key',
                nonce: thaiAiRewriter.nonce
            },
            success: function(response) {
                console.log('Get API key response:', response);
                if (response.success && response.data.has_key) {
                    hasApiKey = true;
                    $apiStatus.removeClass('hidden').html('✅ API Key configured: ' + response.data.masked_key);
                    $rewriteBtn.prop('disabled', false);
                    $processCsvBtn.prop('disabled', false);
                } else {
                    hasApiKey = false;
                    $apiStatus.addClass('hidden');
                    $rewriteBtn.prop('disabled', true);
                    $processCsvBtn.prop('disabled', true);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading API key:', error);
                hasApiKey = false;
                $rewriteBtn.prop('disabled', true);
                $processCsvBtn.prop('disabled', true);
            }
        });
    }
    
    function saveApiKeyToDatabase() {
        console.log('saveApiKeyToDatabase called');
        
        if (typeof thaiAiRewriter === 'undefined') {
            alert('Error: thaiAiRewriter object not found!');
            return;
        }
        
        const apiKey = $apiKeyInput.val().trim();
        console.log('API key entered:', apiKey ? 'Yes' : 'No');
        
        if (!apiKey) {
            alert('Please enter an API key');
            return;
        }
        
        if (!apiKey.startsWith('sk-')) {
            alert('Please enter a valid API key starting with "sk-"');
            return;
        }
        
        const originalText = $saveKeyBtn.text();
        $saveKeyBtn.prop('disabled', true).text('Saving...');
        
        console.log('Sending AJAX request to save API key');
        
        $.ajax({
            url: thaiAiRewriter.ajax_url,
            type: 'POST',
            data: {
                action: 'thai_ai_save_api_key',
                api_key: apiKey,
                nonce: thaiAiRewriter.nonce
            },
            success: function(response) {
                console.log('Save API key response:', response);
                console.log('Response success:', response.success);
                console.log('Response data:', response.data);
                if (response.success) {
                    $apiKeyInput.val('');
                    $apiStatus.removeClass('hidden').html('✅ ' + response.data);
                    loadApiKeyFromDatabase();
                    alert('API key saved successfully!');
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error saving API key:', xhr, status, error);
                console.error('Response text:', xhr.responseText);
                alert('Error saving API key: ' + error);
            },
            complete: function() {
                $saveKeyBtn.prop('disabled', false).text(originalText);
            }
        });
    }
    
    function handleRewrite() {
        const inputText = $originalTextInput.val().trim();
        if (!inputText) {
            alert('Please enter some text to rewrite.');
            return;
        }
        
        if (!hasApiKey) {
            alert('Please configure your API key first.');
            return;
        }
        
        const $button = $rewriteBtn;
        $button.prop('disabled', true).text('Rewriting...');
        
        $.ajax({
            url: thaiAiRewriter.ajax_url,
            type: 'POST',
            data: {
                action: 'thai_ai_rewrite',
                text: inputText,
                nonce: thaiAiRewriter.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayResults(inputText, response.data);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Rewrite error:', error);
                alert('Error rewriting text: ' + error);
            },
            complete: function() {
                $button.prop('disabled', false).text('Rewrite Text');
            }
        });
    }
    
    function handleCsvUpload() {
        console.log('handleCsvUpload called');
        
        const fileInput = $csvFileInput[0];
        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            alert('Please select a CSV file first.');
            return;
        }
        
        const file = fileInput.files[0];
        if (!file.name.toLowerCase().endsWith('.csv')) {
            alert('Please select a CSV file.');
            return;
        }
        
        const formData = new FormData();
        formData.append('csv_file', file);
        formData.append('action', 'thai_ai_upload_csv');
        formData.append('nonce', thaiAiRewriter.nonce);
        
        console.log('Starting CSV upload...');

        // Update upload area text to show file selected
        $csvUploadArea.html('<p class="text-green-600">✓ File selected: ' + file.name + '</p>');

        $.ajax({
            url: thaiAiRewriter.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('CSV upload response:', response);
                if (response.success) {
                    alert('CSV uploaded successfully!');
                    $processCsvBtn.data('session-id', response.data.session_id);
                    $processCsvBtn.prop('disabled', false);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('CSV upload error:', error);
                alert('Error uploading CSV: ' + error);
            }
        });
    }
    
    function handleCsvProcess() {
        console.log('handleCsvProcess called');
        
        if (!hasApiKey) {
            alert('Please configure your API key first.');
            return;
        }
        
        const sessionId = $processCsvBtn.data('session-id');
        if (!sessionId) {
            alert('Please upload a CSV file first.');
            return;
        }
        
        const textColumn = $('#text-column').val() || 0;
        
        const $button = $processCsvBtn;
        const originalText = $button.text();
        $button.prop('disabled', true).text('Processing...');
        
        $.ajax({
            url: thaiAiRewriter.ajax_url,
            type: 'POST',
            data: {
                action: 'thai_ai_process_csv',
                session_id: sessionId,
                text_column: textColumn,
                nonce: thaiAiRewriter.nonce
            },
            success: function(response) {
                console.log('CSV process response:', response);
                if (response.success) {
                    $csvResults.html('<div class="bg-green-100 p-4 rounded">CSV processed successfully! <a href="' + response.data.download_url + '" class="text-blue-600 underline">Download Results</a></div>');
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('CSV process error:', error);
                alert('Error processing CSV: ' + error);
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }
    
    function displayResults(original, rewritten) {
        const resultHtml = '<div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4">' +
            '<h3 class="font-bold mb-2">Original:</h3>' +
            '<p class="mb-4 p-2 bg-gray-100 rounded">' + escapeHtml(original) + '</p>' +
            '<h3 class="font-bold mb-2">Rewritten:</h3>' +
            '<p class="p-2 bg-blue-50 rounded">' + escapeHtml(rewritten) + '</p>' +
            '</div>';
        $resultsContainer.prepend(resultHtml);
        if ($placeholderResults.length) $placeholderResults.hide();
    }
    
    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
    
    console.log('=== THAI AI REWRITER SCRIPT LOADED ===');
});

