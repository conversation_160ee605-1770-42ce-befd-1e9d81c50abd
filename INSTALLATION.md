# Thai AI Rewriter - Installation Guide

🚀 **Get your Thai AI text rewriter up and running in just 3 minutes!**

---

## 📋 Before You Start

### System Requirements
- ✅ WordPress 5.0 or higher
- ✅ PHP 7.4 or higher  
- ✅ Active internet connection
- ✅ WordPress admin access

---

## 🔧 Installation Methods

### Method 1: Manual Upload (Recommended)

#### Step 1: Download the Plugin
- Download the `thai-ai-rewriter` folder
- Make sure all files are included:
  ```
  thai-ai-rewriter/
  ├── thai-ai-rewriter.php
  ├── templates/rewriter-form.php
  ├── assets/script.js
  ├── assets/style.css
  └── README.md
  ```

#### Step 2: Upload to WordPress
1. **Access your website files** via FTP, cPanel File Manager, or hosting control panel
2. **Navigate to** `/wp-content/plugins/`
3. **Upload the entire** `thai-ai-rewriter` folder
4. **Verify the path** is: `/wp-content/plugins/thai-ai-rewriter/`

#### Step 3: Activate the Plugin
1. **Log into WordPress Admin** (yoursite.com/wp-admin)
2. **Go to** `Plugins` → `Installed Plugins`
3. **Find** "Thai AI Rewriter" in the list
4. **Click** `Activate`

✅ **Done! The plugin is now active and ready to use.**

---

### Method 2: ZIP Upload

#### Step 1: Create ZIP File
1. **Compress** the `thai-ai-rewriter` folder into a ZIP file
2. **Name it** `thai-ai-rewriter.zip`

#### Step 2: Upload via WordPress Admin
1. **Go to** `Plugins` → `Add New`
2. **Click** `Upload Plugin`
3. **Choose** your `thai-ai-rewriter.zip` file
4. **Click** `Install Now`
5. **Click** `Activate Plugin`

✅ **Installation complete!**

---

## 🎯 Verify Installation

### Check Admin Menu
1. **Look for** "Thai AI Rewriter" in your WordPress admin sidebar
2. **Click on it** to open the plugin interface
3. **You should see** the "Ready to Use" section

### Test Basic Functionality
1. **Enter some Thai text** in the input field
2. **Click** "Rewrite Text"
3. **Wait for results** (should appear within 5-10 seconds)
4. **Success!** You should see the rewritten text

---

## 🚀 First Use

### Admin Interface
1. **Go to** `Thai AI Rewriter` in your admin menu
2. **Enter Thai text** in the text area
3. **Click** "Rewrite Text"
4. **View results** - original and rewritten text will appear

### Frontend Shortcode
1. **Edit any page or post**
2. **Add the shortcode** `[thai_ai_rewriter]`
3. **Save and view** the page
4. **The rewriter interface** will appear on your website

---

## 🛠️ Troubleshooting

### Plugin Not Appearing in Admin Menu
- **Check** if plugin is activated in `Plugins` → `Installed Plugins`
- **Verify** all plugin files are uploaded correctly
- **Clear cache** if using caching plugins

### "Rewrite Text" Button Not Working
1. **Open browser console** (F12 → Console tab)
2. **Look for JavaScript errors**
3. **Check** if jQuery is loaded on your site
4. **Try** deactivating other plugins temporarily

### API Connection Issues
- **Verify** internet connection is stable
- **Check** WordPress error logs in `/wp-content/debug.log`
- **Ensure** your hosting allows outbound HTTP requests

### Permission Issues
- **Make sure** plugin files have correct permissions:
  - Folders: 755
  - Files: 644
- **Check** with your hosting provider if needed

---

## 🔧 Advanced Configuration

### Enable Debug Mode (Optional)
Add to your `wp-config.php` file:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### File Permissions Check
```bash
# Set correct permissions (if using SSH)
chmod 755 wp-content/plugins/thai-ai-rewriter/
chmod 644 wp-content/plugins/thai-ai-rewriter/*.php
chmod 644 wp-content/plugins/thai-ai-rewriter/assets/*
```

---

## 📱 Mobile & Responsive Testing

### Test on Different Devices
1. **Desktop** - Full functionality
2. **Tablet** - Responsive interface
3. **Mobile** - Touch-friendly buttons
4. **Different browsers** - Chrome, Firefox, Safari, Edge

---

## 🎯 Quick Start Checklist

- [ ] WordPress 5.0+ installed
- [ ] Plugin files uploaded to `/wp-content/plugins/thai-ai-rewriter/`
- [ ] Plugin activated in WordPress admin
- [ ] "Thai AI Rewriter" appears in admin menu
- [ ] Test rewrite functionality works
- [ ] Shortcode `[thai_ai_rewriter]` tested (optional)
- [ ] Mobile responsiveness verified

---

## 🆘 Need Help?

### Common Solutions
1. **Clear browser cache** and try again
2. **Deactivate other plugins** to check for conflicts
3. **Switch to default theme** temporarily
4. **Check error logs** for specific error messages
5. **Verify file permissions** are correct

### Debug Information
When reporting issues, include:
- WordPress version
- PHP version
- Active theme name
- Other active plugins
- Error messages from browser console
- Error messages from WordPress logs

---

## ✅ Installation Complete!

🎉 **Congratulations!** Your Thai AI Rewriter plugin is now installed and ready to use.

### What's Next?
- Start rewriting Thai text immediately
- Add the shortcode to pages where users need text rewriting
- Explore the clean, simple interface
- Enjoy AI-powered Thai text enhancement!

---

**Need more help?** Check the main README.md file for detailed usage instructions and troubleshooting tips.

*Happy rewriting! 🇹🇭✨*