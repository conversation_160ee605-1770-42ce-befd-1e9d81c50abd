body {
    font-family: '<PERSON>bu<PERSON>', sans-serif;
}

.prose-thai {
    font-family: 'Sarabun', sans-serif;
}

.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

[contenteditable]:focus {
    outline: 2px solid #3b82f6;
    border-radius: 4px;
}

.thai-ai-rewriter-container {
    max-width: 1152px;
    margin: 0 auto;
    padding: 1rem;
}

@media (min-width: 768px) {
    .thai-ai-rewriter-container {
        padding: 2rem;
    }
}

.tab-button {
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease-in-out;
}

.tab-button.active {
    border-bottom-color: #3b82f6;
    color: #3b82f6;
}

.tab-content {
    transition: opacity 0.2s ease-in-out;
}

.csv-drag-over {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
}

/* Fix for WordPress table floating issues */
.wp-list-table {
    width: 100% !important;
    clear: both !important;
    margin: 0 !important;
    border-collapse: collapse !important;
    table-layout: fixed !important;
    position: static !important;
    z-index: auto !important;
    float: none !important;
    display: table !important;
}

.wp-list-table.widefat {
    border: 1px solid #c3c4c7 !important;
    box-shadow: none !important;
    width: 100% !important;
}

.wp-list-table.fixed {
    table-layout: fixed !important;
}

.wp-list-table.striped > tbody > tr:nth-child(odd) {
    background-color: #f9f9f9;
}

.wp-list-table th,
.wp-list-table td {
    position: static !important;
    float: none !important;
    display: table-cell !important;
    vertical-align: top !important;
}

.wp-list-table th {
    background: #f1f1f1 !important;
    font-weight: 600 !important;
    border-bottom: 1px solid #c3c4c7 !important;
}

.wp-list-table td {
    border-bottom: 1px solid #c3c4c7 !important;
}

/* Ensure table containers don't float */
.postbox .inside {
    overflow: visible !important;
    position: relative !important;
    clear: both !important;
    display: block !important;
    width: 100% !important;
}

/* Fix for any floating table containers */
div[style*="overflow-x: auto"] {
    position: relative !important;
    clear: both !important;
    display: block !important;
    width: 100% !important;
    overflow-x: auto !important;
    overflow-y: visible !important;
}

/* Specific fix for feedback database tables */
.postbox div[style*="overflow-x: auto"] {
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
}

.postbox div[style*="overflow-x: auto"] .wp-list-table {
    margin: 0 !important;
    background: white !important;
}

/* Clearfix utility */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* Ensure postbox sections don't overlap */
.postbox {
    position: relative !important;
    clear: both !important;
    margin-bottom: 20px !important;
    overflow: visible !important;
}

.postbox .inside {
    position: relative !important;
    z-index: auto !important;
    overflow: visible !important;
}

/* Additional fixes for admin page layout */
.wrap {
    position: relative !important;
    clear: both !important;
}

/* Prevent any absolute positioning issues */
.postbox * {
    position: static !important;
}

.postbox .wp-list-table * {
    position: static !important;
}

/* Force normal document flow */
.postbox,
.postbox .inside,
.postbox .wp-list-table {
    float: none !important;
    position: static !important;
}

/* Additional fixes for admin page table issues */
.thai-ai-rewriter-admin .wp-list-table {
    position: static !important;
    z-index: auto !important;
    transform: none !important;
}

/* Ensure proper stacking context */
.thai-ai-rewriter-admin .postbox {
    isolation: isolate;
    contain: layout;
}

/* Fix for any transform or positioning issues */
.thai-ai-rewriter-admin .postbox .inside table {
    position: static !important;
    transform: none !important;
    will-change: auto !important;
}

/* Prevent table from breaking out of container */
.thai-ai-rewriter-admin .postbox .inside {
    contain: layout style;
    overflow: visible !important;
}

/* Specific fix for feedback database page */
.thai-ai-rewriter-feedback .wp-list-table {
    position: static !important;
    z-index: auto !important;
    transform: none !important;
    will-change: auto !important;
}

.thai-ai-rewriter-feedback .postbox {
    contain: layout;
    isolation: isolate;
}

/* Reset any problematic CSS that might be applied by other plugins */
.thai-ai-rewriter-admin table,
.thai-ai-rewriter-admin .wp-list-table,
.thai-ai-rewriter-admin .postbox table {
    position: static !important;
    float: none !important;
    transform: none !important;
    z-index: auto !important;
    will-change: auto !important;
    contain: none !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;
}

/* Specific fixes for feedback database page tables */
.thai-ai-rewriter-feedback .feedback-table-container,
.thai-ai-rewriter-feedback .csv-table-container {
    position: relative !important;
    clear: both !important;
    display: block !important;
    width: 100% !important;
    overflow-x: auto !important;
    overflow-y: visible !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    z-index: auto !important;
    float: none !important;
    contain: layout !important;
}

.thai-ai-rewriter-feedback .feedback-table-container .wp-list-table,
.thai-ai-rewriter-feedback .csv-table-container .wp-list-table {
    position: static !important;
    float: none !important;
    display: table !important;
    width: 100% !important;
    margin: 0 !important;
    background: white !important;
    transform: none !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;
}

/* Force proper document flow */
.thai-ai-rewriter-feedback .postbox {
    contain: layout style !important;
    isolation: isolate !important;
    position: relative !important;
    display: block !important;
    float: none !important;
}

.thai-ai-rewriter-feedback .postbox .inside {
    position: relative !important;
    display: block !important;
    float: none !important;
    overflow: visible !important;
    contain: layout !important;
}

/* Text Difference Highlighting Styles */
.text-diff-same {
    background-color: #dcfce7;
    color: #166534;
    padding: 2px 4px;
    border-radius: 4px;
    margin: 0 1px;
    font-weight: 500;
    border: 1px solid #bbf7d0;
}

.text-diff-different {
    background-color: #fecaca;
    color: #dc2626;
    padding: 2px 4px;
    border-radius: 4px;
    margin: 0 1px;
    font-weight: 500;
    border: 1px solid #fca5a5;
}

.text-diff-added {
    background-color: #dbeafe;
    color: #1d4ed8;
    padding: 2px 4px;
    border-radius: 4px;
    margin: 0 1px;
    font-weight: 500;
    border: 1px solid #93c5fd;
}

.text-diff-removed {
    background-color: #fed7d7;
    color: #c53030;
    padding: 2px 4px;
    border-radius: 4px;
    margin: 0 1px;
    font-weight: 500;
    border: 1px solid #fc8181;
    text-decoration: line-through;
}

/* Notification styles */
.notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Button styles for difference detection */
.add-alternatives-btn {
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
}

.add-alternatives-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.add-alternatives-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Enhanced result row styling */
.result-row {
    transition: all 0.2s ease-in-out;
}

.result-row:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* Text highlighting improvements */
.original-text, .ai-rewritten-text {
    line-height: 1.6;
    word-spacing: 2px;
}

.original-text span, .ai-rewritten-text span {
    display: inline-block;
    transition: all 0.2s ease-in-out;
}

.original-text span:hover, .ai-rewritten-text span:hover {
    transform: scale(1.05);
    z-index: 10;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Improved contenteditable styling */
[contenteditable="true"] {
    min-height: 40px;
    line-height: 1.6;
}

[contenteditable="true"]:empty:before {
    content: attr(placeholder);
    color: #9ca3af;
    font-style: italic;
}

[contenteditable="true"]:focus:before {
    content: none;
}

/* Difference summary styling */
.result-row .text-xs.text-gray-600 {
    font-family: 'Sarabun', sans-serif;
}

/* Enhanced button states */
.add-alternatives-btn.processing {
    background-color: #f59e0b;
    cursor: wait;
}

.add-alternatives-btn.success {
    background-color: #10b981;
}

/* Grid layout improvements for result rows */
.result-row {
    grid-template-columns: 1fr 1fr 1fr;
    align-items: start;
}

@media (max-width: 768px) {
    .result-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Tooltip for difference highlighting */
.text-diff-same:hover::after,
.text-diff-different:hover::after,
.text-diff-added:hover::after,
.text-diff-removed:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* Loading state for buttons */
.button-loading {
    position: relative;
    color: transparent !important;
}

.button-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}/
* Basic Tailwind-like utility classes */
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-white { background-color: #ffffff; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-700 { background-color: #1d4ed8; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-700 { background-color: #374151; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-600 { background-color: #16a34a; }
.bg-green-700 { background-color: #15803d; }
.bg-red-600 { background-color: #dc2626; }
.bg-red-700 { background-color: #b91c1c; }
.bg-purple-600 { background-color: #9333ea; }
.bg-purple-700 { background-color: #7c3aed; }

.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-blue-600 { color: #2563eb; }
.text-white { color: #ffffff; }
.text-green-700 { color: #15803d; }
.text-red-600 { color: #dc2626; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }

.border { border-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-blue-500 { border-color: #3b82f6; }
.border-green-500 { border-color: #22c55e; }
.border-l-4 { border-left-width: 4px; }

.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-r-lg { border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

.m-2 { margin: 0.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-[40px] { min-height: 40px; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }

.text-center { text-align: center; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }

.hidden { display: none; }
.block { display: block; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }

.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-gray-500:hover { background-color: #6b7280; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:bg-green-700:hover { background-color: #15803d; }
.hover\:bg-red-600:hover { background-color: #dc2626; }
.hover\:bg-purple-700:hover { background-color: #7c3aed; }

.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-blue-500:focus { --tw-ring-color: #3b82f6; }
.focus\:ring-gray-500:focus { --tw-ring-color: #6b7280; }
.focus\:ring-green-500:focus { --tw-ring-color: #22c55e; }
.focus\:ring-red-500:focus { --tw-ring-color: #ef4444; }
.focus\:ring-purple-500:focus { --tw-ring-color: #a855f7; }
.focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }
.focus\:border-blue-500:focus { border-color: #3b82f6; }
.focus\:border-red-500:focus { border-color: #ef4444; }

.disabled\:bg-gray-400:disabled { background-color: #9ca3af; }

/* Responsive classes */
@media (min-width: 768px) {
    .md\:flex-row { flex-direction: row; }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

/* Container styles */
.thai-ai-rewriter-container {
    max-width: 1152px;
    margin: 0 auto;
    padding: 1rem;
}

@media (min-width: 768px) {
    .thai-ai-rewriter-container {
        padding: 2rem;
    }
}