<div class="thai-ai-rewriter-container bg-gray-50 text-gray-800">
    <header class="text-center mb-8">
        <h1 class="text-4xl font-bold text-blue-600">🇹🇭 Thai AI Rewriter</h1>
        <p class="text-lg text-gray-600 mt-2">Refine your Thai text with an AI that learns from your style.</p>
    </header>

    <!-- Configuration Section -->
    <section id="config-section" class="bg-white p-6 rounded-xl shadow-md mb-8 border border-gray-200">
        <h2 class="text-xl font-bold mb-4 text-gray-700">1. Configuration</h2>
        
        <div class="flex flex-col md:flex-row items-center gap-4">
            <div class="w-full">
                <label for="api-key" class="block text-sm font-medium text-gray-700 mb-1">OpenTyphoon API Key</label>
                <input type="password" id="api-key" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Enter your sk-typhoon-xxxxxxxx key">
            </div>
            <button id="save-key-btn" 
                class="w-full md:w-auto bg-blue-600 text-white font-semibold py-2 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out mt-2 md:mt-5">Save Key</button>
        </div>
        
        <p id="api-status" class="text-sm mt-3 text-green-600 font-medium hidden">✅ API Key saved for this session.</p>
        
        <div class="mt-4 p-3 bg-blue-100 border-l-4 border-blue-500 text-blue-700 rounded-r-lg">
            <p class="text-sm"><strong>Security Note:</strong> Your API key is securely stored in your WordPress database and only used for OpenTyphoon API calls.</p>
        </div>
    </section>

    <!-- Input Section -->
    <section class="bg-white p-6 rounded-xl shadow-md mb-8 border border-gray-200">
        <h2 class="text-xl font-bold mb-4 text-gray-700">2. Input Options</h2>

        <!-- Tab Navigation -->
        <div class="flex border-b border-gray-200 mb-4">
            <button id="text-tab"
                class="tab-button active px-4 py-2 font-medium text-blue-600 border-b-2 border-blue-600">Manual Text
                Input</button>
            <button id="csv-tab" class="tab-button px-4 py-2 font-medium text-gray-500 hover:text-gray-700">CSV
                Upload</button>
        </div>

        <!-- Manual Text Input Tab -->
        <div id="text-input-section" class="tab-content">
            <p class="text-sm text-gray-500 mb-3">Enter the Thai text you want to rewrite. Each new line will be treated
                as a separate sentence.</p>
            <textarea id="original-text-input" rows="8"
                class="w-full p-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 prose-thai"
                placeholder="เช่น: แมวนั่งอยู่บนเสื่อ"></textarea>
            <button id="rewrite-btn"
                class="mt-4 w-full bg-green-600 text-white font-semibold py-3 px-6 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out flex items-center justify-center gap-2 disabled:bg-gray-400"
                disabled>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="feather feather-feather">
                    <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path>
                    <line x1="16" y1="8" x2="2" y2="22"></line>
                    <line x1="17.5" y1="15" x2="9" y2="15"></line>
                </svg>
                Rewrite Text
            </button>
        </div>

        <!-- CSV Upload Tab -->
        <div id="csv-input-section" class="tab-content hidden">
            <p class="text-sm text-gray-500 mb-3">Upload a CSV file to rewrite text in bulk. The system will add a new
                column with AI-rewritten content.</p>

            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input type="file" id="csv-file-input" accept=".csv" class="hidden">
                <div id="csv-upload-area" class="cursor-pointer">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-600">
                        <span class="font-medium text-blue-600 hover:text-blue-500">Click to upload</span> or drag and
                        drop
                    </p>
                    <p class="text-xs text-gray-500">CSV files only</p>
                </div>
            </div>

            <div id="csv-preview" class="hidden mt-4">
                <h3 class="text-lg font-medium text-gray-700 mb-2">CSV Preview</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-sm text-gray-600 mb-2">
                        File: <span id="csv-filename" class="font-medium"></span>
                        (<span id="csv-row-count"></span> rows)
                    </p>
                    <div class="mb-4">
                        <label for="text-column-select" class="block text-sm font-medium text-gray-700 mb-1">Select text
                            column to rewrite:</label>
                        <select id="text-column-select"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </select>
                    </div>
                    <div id="csv-preview-table"
                        class="overflow-x-auto max-h-40 overflow-y-auto border border-gray-200 rounded"></div>
                </div>
                <button id="process-csv-btn"
                    class="mt-4 w-full bg-purple-600 text-white font-semibold py-3 px-6 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition duration-150 ease-in-out flex items-center justify-center gap-2 disabled:bg-gray-400"
                    disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Process CSV with AI
                </button>
            </div>

            <div id="csv-processing" class="hidden mt-4 text-center">
                <div class="loader mx-auto"></div>
                <p class="mt-2 text-gray-600">Processing CSV file... This may take a while.</p>
            </div>

            <div id="csv-complete" class="hidden mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <p class="ml-2 text-sm text-green-700">
                        CSV processed successfully! <span id="processed-count"></span> rows processed.
                    </p>
                </div>
                <button id="download-csv-btn"
                    class="mt-3 bg-green-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                    Download Processed CSV
                </button>
            </div>
        </div>
    </section>

    <!-- Results Section -->
    <section>
        <h2 class="text-2xl font-bold mb-4 text-center text-gray-700">3. Review and Edit</h2>
        <div id="results-header"
            class="hidden grid-cols-1 md:grid-cols-3 gap-4 mb-2 text-center font-bold text-gray-600">
            <div class="p-2 bg-gray-200 rounded-t-lg">Original Text</div>
            <div class="p-2 bg-gray-200 rounded-t-lg">AI Rewritten</div>
            <div class="p-2 bg-gray-200 rounded-t-lg">Your Approved Version</div>
        </div>
        <div id="results-container" class="space-y-4">
            <!-- Dynamic content will be inserted here -->
        </div>

        <!-- Combined Version Section -->
        <div id="combined-section"
            class="hidden mt-8 bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-xl shadow-md border border-green-200">
            <h3 class="text-xl font-bold mb-4 text-green-700 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4"></path>
                    <path d="M15 11h4a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-4"></path>
                    <path d="M12 15V9a4 4 0 0 0-8 0v6"></path>
                    <path d="M12 15V9a4 4 0 0 1 8 0v6"></path>
                </svg>
                Combined Final Version
            </h3>
            <div id="combined-content"
                class="bg-white p-4 rounded-lg border border-gray-200 prose-thai text-gray-800 leading-relaxed">
                <!-- Combined text will appear here -->
            </div>
            <div class="flex gap-3 mt-4">
                <button id="save-combined-btn"
                    class="bg-green-600 text-white font-semibold py-2 px-6 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17,21 17,13 7,13 7,21"></polyline>
                        <polyline points="7,3 7,8 15,8"></polyline>
                    </svg>
                    Save to Work History
                </button>
                <button id="copy-combined-btn"
                    class="bg-blue-600 text-white font-semibold py-2 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    Copy to Clipboard
                </button>
                <button id="export-differences-btn"
                    class="bg-purple-600 text-white font-semibold py-2 px-6 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition duration-150 ease-in-out flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Export Differences
                </button>
            </div>
        </div>

        <div id="loading-indicator" class="hidden flex flex-col items-center justify-center p-8 text-gray-500">
            <div class="loader"></div>
            <p class="mt-4">AI is thinking... Please wait.</p>
        </div>
        <div id="placeholder-results"
            class="text-center py-12 px-6 bg-white rounded-xl shadow-md border border-gray-200">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                class="mx-auto text-gray-400">
                <path d="M12 22h6a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v5"></path>
                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                <path d="M2.5 13.6a1 1 0 0 0-1 1.7l1.4 1.4a1 1 0 0 0 1.8-1.2l-1.2-2.3a1 1 0 0 0-1-.6Z"></path>
                <path d="m3 19 2-2"></path>
                <path d="M5 17v2h2"></path>
            </svg>
            <h3 class="mt-2 text-lg font-medium text-gray-800">Your results will appear here</h3>
            <p class="mt-1 text-sm text-gray-500">Enter some text above and click "Rewrite" to get started.</p>
        </div>
    </section>

    <!-- AI Instructions Management -->
    <section class="mt-12">
        <div class="bg-white rounded-xl shadow-md border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-bold text-gray-700 mb-4">🤖 AI Instructions Database</h3>
            <p class="text-sm text-gray-600 mb-4">จัดการคำแนะนำที่เก็บไว้ในฐานข้อมูลสำหรับ AI</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button id="view-stored-instructions-btn"
                    class="bg-indigo-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition">
                    ดูคำแนะนำที่เก็บไว้
                </button>
                <button id="refresh-instructions-btn"
                    class="bg-green-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition">
                    อัพเดทคำแนะนำ
                </button>
            </div>

            <div id="stored-instructions-viewer" class="hidden mt-4 p-4 bg-gray-50 rounded-lg max-h-60 overflow-y-auto">
                <h4 class="text-sm font-medium text-gray-700 mb-2">คำแนะนำที่เก็บในฐานข้อมูล:</h4>
                <div id="stored-instructions-content" class="text-sm text-gray-600">
                    <p>กำลังโหลด...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Word Management Section -->
    <section class="mt-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Word Alternatives -->
            <div class="bg-white rounded-xl shadow-md border border-gray-200 p-6">
                <h3 class="text-lg font-bold text-gray-700 mb-4">🔄 คำทางเลือก (Word Alternatives)</h3>
                <p class="text-sm text-gray-600 mb-4">กำหนดคำทางเลือกในรูปแบบ: คำ1|คำ2|คำ3 เพื่อให้ AI สุ่มเลือกใช้</p>

                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">คำเดิม</label>
                        <input type="text" id="original-word"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="เช่น: ดีมาก">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">คำทางเลือก (คั่นด้วย |)</label>
                        <input type="text" id="alternative-words"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="เช่น: ยอดเยี่ยม|เลิศ|สุดยอด">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">คำอธิบาย (ไม่บังคับ)</label>
                        <input type="text" id="word-description"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="เช่น: คำชมเชยระดับสูง">
                    </div>
                    <button id="save-alternatives-btn"
                        class="w-full bg-blue-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition">
                        บันทึกคำทางเลือก
                    </button>
                </div>

                <div id="alternatives-list" class="mt-4 max-h-40 overflow-y-auto">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">รายการคำทางเลือก:</h4>
                    <div id="alternatives-content" class="text-sm text-gray-600">
                        <p>กำลังโหลด...</p>
                    </div>
                </div>
            </div>

            <!-- Forbidden Words -->
            <div class="bg-white rounded-xl shadow-md border border-gray-200 p-6">
                <h3 class="text-lg font-bold text-gray-700 mb-4">🚫 คำต้องห้าม (Forbidden Words)</h3>
                <p class="text-sm text-gray-600 mb-4">กำหนดคำที่ไม่ต้องการให้ปรากฏในการ rewrite</p>

                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">คำต้องห้าม</label>
                        <input type="text" id="forbidden-word"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500"
                            placeholder="เช่น: แย่มาก">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">เหตุผล (ไม่บังคับ)</label>
                        <input type="text" id="forbidden-reason"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500"
                            placeholder="เช่น: คำที่ไม่สุภาพ">
                    </div>
                    <button id="save-forbidden-btn"
                        class="w-full bg-red-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition">
                        บันทึกคำต้องห้าม
                    </button>
                </div>

                <div id="forbidden-list" class="mt-4 max-h-40 overflow-y-auto">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">รายการคำต้องห้าม:</h4>
                    <div id="forbidden-content" class="text-sm text-gray-600">
                        <p>กำลังโหลด...</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Feedback Database Viewer -->
    <section class="mt-8">
        <details class="bg-white rounded-xl shadow-md border border-gray-200 p-4">
            <summary class="font-bold text-lg cursor-pointer text-gray-700">View Feedback Database (<span
                    id="db-count">0</span> entries)</summary>
            <div id="db-viewer" class="mt-4 text-sm text-gray-600 max-h-60 overflow-y-auto p-3 bg-gray-50 rounded-lg">
                <p>The approved rewrites you save will appear here. They are used to improve future AI suggestions.</p>
            </div>
            <button id="clear-db-btn"
                class="mt-4 text-sm bg-red-500 text-white font-semibold py-1 px-3 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition">Clear
                Database</button>
        </details>
    </section>
</div>